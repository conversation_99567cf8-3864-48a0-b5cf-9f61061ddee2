using AutoMapper;
using ERP.Application.Common.Interfaces;
using ERP.Domain.Entities.Sales;
using ERP.DTOs.Sales;
using ERP.Shared.Extensions;
using ERP.Shared.Models;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace ERP.Application.Features.Sales.Commands;

public class CreateSalesInvoiceCommand : CreateSalesInvoiceRequest, IRequest<ApiResponse<SalesInvoiceDto>>
{
}

public class CreateSalesInvoiceCommandHandler : IRequestHandler<CreateSalesInvoiceCommand, ApiResponse<SalesInvoiceDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CreateSalesInvoiceCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<ApiResponse<SalesInvoiceDto>> Handle(CreateSalesInvoiceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var companyId = _httpContextAccessor.HttpContext?.User.GetCompanyId() ?? Guid.Empty;
            var userId = _httpContextAccessor.HttpContext?.User.GetUserId() ?? Guid.Empty;

            if (companyId == Guid.Empty)
            {
                return ApiResponse<SalesInvoiceDto>.ErrorResult("Company not found");
            }

            // Validate customer
            var customer = await _unitOfWork.Customers.GetByIdAsync(request.CustomerId, cancellationToken);
            if (customer == null || customer.CompanyId != companyId)
            {
                return ApiResponse<SalesInvoiceDto>.ErrorResult("Invalid customer");
            }

            // Get main branch (for now, we'll use the main branch)
            var branch = await _unitOfWork.Branches.FirstOrDefaultAsync(
                b => b.CompanyId == companyId && b.IsMainBranch, cancellationToken);
            
            if (branch == null)
            {
                return ApiResponse<SalesInvoiceDto>.ErrorResult("No main branch found");
            }

            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            // Generate invoice number
            var invoiceCount = await _unitOfWork.SalesInvoices.CountAsync(
                si => si.CompanyId == companyId, cancellationToken);
            var invoiceNumber = $"INV-{DateTime.Now.Year}-{(invoiceCount + 1):D6}";

            // Create sales invoice
            var salesInvoice = new SalesInvoice
            {
                InvoiceNumber = invoiceNumber,
                InvoiceDate = request.InvoiceDate,
                DueDate = request.DueDate,
                CustomerId = request.CustomerId,
                Reference = request.Reference,
                CurrencyCode = request.CurrencyCode,
                ExchangeRate = request.ExchangeRate,
                Notes = request.Notes,
                CompanyId = companyId,
                BranchId = branch.Id,
                CreatedBy = userId,
                CreatedAt = DateTime.UtcNow
            };

            var createdInvoice = await _unitOfWork.SalesInvoices.AddAsync(salesInvoice, cancellationToken);

            decimal subTotal = 0;
            decimal totalTaxAmount = 0;
            decimal totalDiscountAmount = 0;

            // Process invoice lines
            foreach (var lineRequest in request.Lines)
            {
                // Validate item
                var item = await _unitOfWork.Items.GetByIdAsync(lineRequest.ItemId, 
                    i => i.ItemTaxes, i => i.ItemDiscounts);
                
                if (item == null || item.CompanyId != companyId)
                {
                    await _unitOfWork.RollbackTransactionAsync(cancellationToken);
                    return ApiResponse<SalesInvoiceDto>.ErrorResult($"Invalid item: {lineRequest.ItemId}");
                }

                var lineTotal = lineRequest.Quantity * lineRequest.UnitPrice;
                subTotal += lineTotal;

                // Create invoice line
                var invoiceLine = new SalesInvoiceLine
                {
                    SalesInvoiceId = createdInvoice.Id,
                    ItemId = lineRequest.ItemId,
                    Quantity = lineRequest.Quantity,
                    UnitPrice = lineRequest.UnitPrice,
                    LineTotal = lineTotal,
                    Notes = lineRequest.Notes,
                    UnitId = lineRequest.UnitId,
                    BatchNumber = lineRequest.BatchNumber,
                    SerialNumber = lineRequest.SerialNumber,
                    ExpiryDate = lineRequest.ExpiryDate,
                    CreatedBy = userId,
                    CreatedAt = DateTime.UtcNow
                };

                var createdLine = await _unitOfWork.SalesInvoiceLines.AddAsync(invoiceLine, cancellationToken);

                // Apply item taxes
                foreach (var itemTax in item.ItemTaxes.Where(it => it.IsApplicable))
                {
                    var taxConfig = await _unitOfWork.TaxConfigurations.GetByIdAsync(itemTax.TaxConfigurationId);
                    if (taxConfig != null && taxConfig.IsActive)
                    {
                        var taxAmount = lineTotal * (taxConfig.TaxRate / 100);
                        totalTaxAmount += taxAmount;

                        var lineTax = new SalesInvoiceLineTax
                        {
                            SalesInvoiceLineId = createdLine.Id,
                            TaxConfigurationId = taxConfig.Id,
                            TaxableAmount = lineTotal,
                            TaxAmount = taxAmount,
                            CreatedBy = userId,
                            CreatedAt = DateTime.UtcNow
                        };

                        await _unitOfWork.SalesInvoiceLineTaxes.AddAsync(lineTax, cancellationToken);
                    }
                }

                // Apply line discounts
                foreach (var discountRequest in lineRequest.Discounts)
                {
                    decimal discountAmount = 0;
                    if (discountRequest.DiscountType == Domain.Enums.DiscountType.Percentage)
                    {
                        discountAmount = lineTotal * (discountRequest.DiscountValue / 100);
                    }
                    else
                    {
                        discountAmount = discountRequest.DiscountValue;
                    }

                    totalDiscountAmount += discountAmount;

                    var lineDiscount = new SalesInvoiceLineDiscount
                    {
                        SalesInvoiceLineId = createdLine.Id,
                        DiscountConfigurationId = discountRequest.DiscountConfigurationId,
                        DiscountType = discountRequest.DiscountType,
                        DiscountValue = discountRequest.DiscountValue,
                        DiscountAmount = discountAmount,
                        CreatedBy = userId,
                        CreatedAt = DateTime.UtcNow
                    };

                    await _unitOfWork.SalesInvoiceLineDiscounts.AddAsync(lineDiscount, cancellationToken);
                }
            }

            // Apply invoice-level discounts
            foreach (var discountRequest in request.Discounts)
            {
                decimal discountAmount = 0;
                if (discountRequest.DiscountType == Domain.Enums.DiscountType.Percentage)
                {
                    discountAmount = subTotal * (discountRequest.DiscountValue / 100);
                }
                else
                {
                    discountAmount = discountRequest.DiscountValue;
                }

                totalDiscountAmount += discountAmount;

                var invoiceDiscount = new SalesInvoiceDiscount
                {
                    SalesInvoiceId = createdInvoice.Id,
                    DiscountConfigurationId = discountRequest.DiscountConfigurationId,
                    DiscountType = discountRequest.DiscountType,
                    DiscountValue = discountRequest.DiscountValue,
                    DiscountAmount = discountAmount,
                    CreatedBy = userId,
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.SalesInvoiceDiscounts.AddAsync(invoiceDiscount, cancellationToken);
            }

            // Update invoice totals
            createdInvoice.SubTotal = subTotal;
            createdInvoice.TotalTaxAmount = totalTaxAmount;
            createdInvoice.TotalDiscountAmount = totalDiscountAmount;
            createdInvoice.TotalAmount = subTotal + totalTaxAmount - totalDiscountAmount;

            await _unitOfWork.SalesInvoices.UpdateAsync(createdInvoice, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            // Retrieve the created invoice with all related data
            var invoiceWithIncludes = await _unitOfWork.SalesInvoices.GetByIdAsync(createdInvoice.Id,
                si => si.Customer,
                si => si.SalesInvoiceLines,
                si => si.SalesInvoiceTaxes,
                si => si.SalesInvoiceDiscounts);

            var result = _mapper.Map<SalesInvoiceDto>(invoiceWithIncludes);
            return ApiResponse<SalesInvoiceDto>.SuccessResult(result, "Sales invoice created successfully");
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            return ApiResponse<SalesInvoiceDto>.ErrorResult($"Error creating sales invoice: {ex.Message}");
        }
    }
}
