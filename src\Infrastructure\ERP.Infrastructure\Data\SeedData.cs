using ERP.Domain.Entities.Company;
using ERP.Domain.Entities.Configuration;
using ERP.Domain.Entities.Customers;
using ERP.Domain.Entities.Inventory;
using ERP.Domain.Enums;
using ERP.Identity.Data;
using ERP.Identity.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace ERP.Infrastructure.Data;

public static class SeedData
{
    public static async Task InitializeAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

        try
        {
            // Seed Identity Data
            await SeedIdentityAsync(services);
            
            // Seed Application Data
            await SeedApplicationAsync(services);
            
            logger.LogInformation("Database seeding completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the database");
            throw;
        }
    }

    private static async Task SeedIdentityAsync(IServiceProvider services)
    {
        var identityContext = services.GetRequiredService<IdentityDbContext>();
        var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = services.GetRequiredService<RoleManager<ApplicationRole>>();

        await identityContext.Database.MigrateAsync();

        // Create default company first (we'll need the ID)
        var applicationContext = services.GetRequiredService<ApplicationDbContext>();
        await applicationContext.Database.MigrateAsync();

        var defaultCompany = await applicationContext.Companies.FirstOrDefaultAsync();
        if (defaultCompany == null)
        {
            defaultCompany = new Company
            {
                Id = Guid.NewGuid(),
                CompanyCode = "DEMO001",
                CompanyNameEn = "Demo Company Ltd",
                CompanyNameAr = "شركة العرض التوضيحي المحدودة",
                TaxNumber = "*********",
                CommercialRegistration = "CR*********",
                Email = "<EMAIL>",
                Phone = "+966*********",
                Address = "123 Business District",
                City = "Riyadh",
                Country = "Saudi Arabia",
                PostalCode = "12345",
                BaseCurrency = "SAR",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = Guid.Empty
            };
            applicationContext.Companies.Add(defaultCompany);
            await applicationContext.SaveChangesAsync();
        }

        // Create roles
        var roles = new[]
        {
            new ApplicationRole { Name = "SuperAdmin", Description = "Super Administrator", CompanyId = defaultCompany.Id },
            new ApplicationRole { Name = "Admin", Description = "Administrator", CompanyId = defaultCompany.Id },
            new ApplicationRole { Name = "Manager", Description = "Manager", CompanyId = defaultCompany.Id },
            new ApplicationRole { Name = "User", Description = "Regular User", CompanyId = defaultCompany.Id }
        };

        foreach (var role in roles)
        {
            if (!await roleManager.RoleExistsAsync(role.Name!))
            {
                await roleManager.CreateAsync(role);
            }
        }

        // Create default admin user
        var adminEmail = "<EMAIL>";
        var adminUser = await userManager.FindByEmailAsync(adminEmail);
        
        if (adminUser == null)
        {
            adminUser = new ApplicationUser
            {
                UserName = adminEmail,
                Email = adminEmail,
                FirstName = "System",
                LastName = "Administrator",
                CompanyId = defaultCompany.Id,
                IsActive = true,
                EmailConfirmed = true
            };

            var result = await userManager.CreateAsync(adminUser, "Admin123!");
            if (result.Succeeded)
            {
                await userManager.AddToRoleAsync(adminUser, "SuperAdmin");
            }
        }
    }

    private static async Task SeedApplicationAsync(IServiceProvider services)
    {
        var context = services.GetRequiredService<ApplicationDbContext>();
        var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();

        var defaultCompany = await context.Companies.FirstAsync();
        var adminUser = await userManager.FindByEmailAsync("<EMAIL>");

        // Create default branch
        if (!await context.Branches.AnyAsync())
        {
            var mainBranch = new Branch
            {
                BranchCode = "MAIN",
                BranchNameEn = "Main Branch",
                BranchNameAr = "الفرع الرئيسي",
                Email = "<EMAIL>",
                Phone = "+966*********",
                Address = "123 Business District",
                City = "Riyadh",
                Country = "Saudi Arabia",
                PostalCode = "12345",
                IsActive = true,
                IsMainBranch = true,
                CompanyId = defaultCompany.Id,
                CreatedBy = adminUser!.Id,
                CreatedAt = DateTime.UtcNow
            };
            context.Branches.Add(mainBranch);
        }

        // Create tax configurations
        if (!await context.TaxConfigurations.AnyAsync())
        {
            var taxes = new[]
            {
                new TaxConfiguration
                {
                    TaxCode = "VAT",
                    TaxNameEn = "Value Added Tax",
                    TaxNameAr = "ضريبة القيمة المضافة",
                    TaxRate = 15.0m,
                    TaxType = TaxType.VAT,
                    IsActive = true,
                    IsDefault = true,
                    CompanyId = defaultCompany.Id,
                    CreatedBy = adminUser!.Id,
                    CreatedAt = DateTime.UtcNow
                },
                new TaxConfiguration
                {
                    TaxCode = "ST",
                    TaxNameEn = "Sales Tax",
                    TaxNameAr = "ضريبة المبيعات",
                    TaxRate = 5.0m,
                    TaxType = TaxType.SalesTax,
                    IsActive = true,
                    IsDefault = false,
                    CompanyId = defaultCompany.Id,
                    CreatedBy = adminUser!.Id,
                    CreatedAt = DateTime.UtcNow
                }
            };
            context.TaxConfigurations.AddRange(taxes);
        }

        // Create discount configurations
        if (!await context.DiscountConfigurations.AnyAsync())
        {
            var discounts = new[]
            {
                new DiscountConfiguration
                {
                    DiscountCode = "BULK10",
                    DiscountNameEn = "Bulk Discount 10%",
                    DiscountNameAr = "خصم الكمية 10%",
                    DiscountType = DiscountType.Percentage,
                    DiscountValue = 10.0m,
                    MinimumAmount = 1000.0m,
                    MaximumDiscount = 500.0m,
                    IsActive = true,
                    CompanyId = defaultCompany.Id,
                    CreatedBy = adminUser!.Id,
                    CreatedAt = DateTime.UtcNow
                },
                new DiscountConfiguration
                {
                    DiscountCode = "FIXED50",
                    DiscountNameEn = "Fixed Discount 50 SAR",
                    DiscountNameAr = "خصم ثابت 50 ريال",
                    DiscountType = DiscountType.FixedAmount,
                    DiscountValue = 50.0m,
                    MinimumAmount = 200.0m,
                    MaximumDiscount = 50.0m,
                    IsActive = true,
                    CompanyId = defaultCompany.Id,
                    CreatedBy = adminUser!.Id,
                    CreatedAt = DateTime.UtcNow
                }
            };
            context.DiscountConfigurations.AddRange(discounts);
        }

        // Create units
        if (!await context.Units.AnyAsync())
        {
            var units = new[]
            {
                new Unit { UnitCode = "PCS", UnitNameEn = "Pieces", UnitNameAr = "قطعة", Symbol = "pcs", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new Unit { UnitCode = "KG", UnitNameEn = "Kilogram", UnitNameAr = "كيلوجرام", Symbol = "kg", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new Unit { UnitCode = "LTR", UnitNameEn = "Liter", UnitNameAr = "لتر", Symbol = "L", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new Unit { UnitCode = "BOX", UnitNameEn = "Box", UnitNameAr = "صندوق", Symbol = "box", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id }
            };
            context.Units.AddRange(units);
        }

        // Create item categories
        if (!await context.ItemCategories.AnyAsync())
        {
            var categories = new[]
            {
                new ItemCategory { CategoryCode = "ELEC", CategoryNameEn = "Electronics", CategoryNameAr = "إلكترونيات", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new ItemCategory { CategoryCode = "FURN", CategoryNameEn = "Furniture", CategoryNameAr = "أثاث", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new ItemCategory { CategoryCode = "STAT", CategoryNameEn = "Stationery", CategoryNameAr = "قرطاسية", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id }
            };
            context.ItemCategories.AddRange(categories);
        }

        // Create item groups
        if (!await context.ItemGroups.AnyAsync())
        {
            var groups = new[]
            {
                new ItemGroup { GroupCode = "COMP", GroupNameEn = "Computers", GroupNameAr = "حاسوب", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new ItemGroup { GroupCode = "MOBL", GroupNameEn = "Mobile Devices", GroupNameAr = "أجهزة محمولة", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new ItemGroup { GroupCode = "OFFC", GroupNameEn = "Office Equipment", GroupNameAr = "معدات مكتبية", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id }
            };
            context.ItemGroups.AddRange(groups);
        }

        // Create brands
        if (!await context.Brands.AnyAsync())
        {
            var brands = new[]
            {
                new Brand { BrandNameEn = "Dell", BrandNameAr = "ديل", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new Brand { BrandNameEn = "HP", BrandNameAr = "اتش بي", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new Brand { BrandNameEn = "Apple", BrandNameAr = "أبل", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new Brand { BrandNameEn = "Samsung", BrandNameAr = "سامسونج", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id }
            };
            context.Brands.AddRange(brands);
        }

        // Create manufacturers
        if (!await context.Manufacturers.AnyAsync())
        {
            var manufacturers = new[]
            {
                new Manufacturer { ManufacturerNameEn = "Dell Technologies", ManufacturerNameAr = "تقنيات ديل", ContactInfo = "<EMAIL>", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new Manufacturer { ManufacturerNameEn = "HP Inc.", ManufacturerNameAr = "شركة اتش بي", ContactInfo = "<EMAIL>", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id },
                new Manufacturer { ManufacturerNameEn = "Apple Inc.", ManufacturerNameAr = "شركة أبل", ContactInfo = "<EMAIL>", IsActive = true, CompanyId = defaultCompany.Id, CreatedBy = adminUser!.Id }
            };
            context.Manufacturers.AddRange(manufacturers);
        }

        // Create sample customers
        if (!await context.Customers.AnyAsync())
        {
            var customers = new[]
            {
                new Customer
                {
                    CustomerCode = "CUST001",
                    CustomerNameEn = "ABC Trading Company",
                    CustomerNameAr = "شركة أي بي سي للتجارة",
                    Email = "<EMAIL>",
                    Phone = "+966*********",
                    Mobile = "+966501234567",
                    TaxNumber = "300*********003",
                    Address = "123 Business Street",
                    City = "Riyadh",
                    Country = "Saudi Arabia",
                    PostalCode = "12345",
                    CreditLimit = 50000.0m,
                    PaymentTerms = 30,
                    IsActive = true,
                    CompanyId = defaultCompany.Id,
                    CreatedBy = adminUser!.Id,
                    CreatedAt = DateTime.UtcNow
                },
                new Customer
                {
                    CustomerCode = "CUST002",
                    CustomerNameEn = "XYZ Corporation",
                    CustomerNameAr = "مؤسسة إكس واي زد",
                    Email = "<EMAIL>",
                    Phone = "+966123456790",
                    Mobile = "+966501234568",
                    TaxNumber = "300*********004",
                    Address = "456 Commerce Avenue",
                    City = "Jeddah",
                    Country = "Saudi Arabia",
                    PostalCode = "23456",
                    CreditLimit = 75000.0m,
                    PaymentTerms = 45,
                    IsActive = true,
                    CompanyId = defaultCompany.Id,
                    CreatedBy = adminUser!.Id,
                    CreatedAt = DateTime.UtcNow
                }
            };
            context.Customers.AddRange(customers);
        }

        await context.SaveChangesAsync();
    }
}
