using ERP.Domain.Enums;

namespace ERP.DTOs.Configuration;

public class DiscountConfigurationDto
{
    public Guid Id { get; set; }
    public string DiscountCode { get; set; } = string.Empty;
    public string DiscountNameEn { get; set; } = string.Empty;
    public string DiscountNameAr { get; set; } = string.Empty;
    public DiscountType DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public decimal MinimumAmount { get; set; }
    public decimal MaximumDiscount { get; set; }
    public bool IsActive { get; set; }
    public Guid CompanyId { get; set; }
}

public class CreateDiscountConfigurationRequest
{
    public string DiscountCode { get; set; } = string.Empty;
    public string DiscountNameEn { get; set; } = string.Empty;
    public string DiscountNameAr { get; set; } = string.Empty;
    public DiscountType DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
    public DateTime? Valid<PERSON>rom { get; set; }
    public DateTime? ValidTo { get; set; }
    public decimal MinimumAmount { get; set; }
    public decimal MaximumDiscount { get; set; }
    public bool IsActive { get; set; } = true;
}

public class UpdateDiscountConfigurationRequest : CreateDiscountConfigurationRequest
{
    public Guid Id { get; set; }
}
