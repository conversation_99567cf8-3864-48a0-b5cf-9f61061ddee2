using ERP.Domain.Entities.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ERP.Infrastructure.Data.Configurations;

public class SalesInvoiceConfiguration : IEntityTypeConfiguration<SalesInvoice>
{
    public void Configure(EntityTypeBuilder<SalesInvoice> builder)
    {
        builder.HasKey(si => si.Id);

        builder.Property(si => si.InvoiceNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(si => si.Reference)
            .HasMaxLength(100);

        builder.Property(si => si.SubTotal)
            .HasColumnType("decimal(18,4)");

        builder.Property(si => si.TotalTaxAmount)
            .HasColumnType("decimal(18,4)");

        builder.Property(si => si.TotalDiscountAmount)
            .HasColumnType("decimal(18,4)");

        builder.Property(si => si.TotalAmount)
            .HasColumnType("decimal(18,4)");

        builder.Property(si => si.CurrencyCode)
            .IsRequired()
            .HasMaxLength(3)
            .HasDefaultValue("SAR");

        builder.Property(si => si.ExchangeRate)
            .HasColumnType("decimal(18,6)")
            .HasDefaultValue(1);

        builder.Property(si => si.Notes)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(si => new { si.InvoiceNumber, si.CompanyId }).IsUnique();
        builder.HasIndex(si => si.InvoiceDate);
        builder.HasIndex(si => si.DueDate);
        builder.HasIndex(si => si.Status);

        // Relationships
        builder.HasOne(si => si.Customer)
            .WithMany(c => c.SalesInvoices)
            .HasForeignKey(si => si.CustomerId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(si => si.Branch)
            .WithMany(b => b.SalesInvoices)
            .HasForeignKey(si => si.BranchId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(si => si.Company)
            .WithMany(c => c.SalesInvoices)
            .HasForeignKey(si => si.CompanyId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(si => si.SalesInvoiceLines)
            .WithOne(sil => sil.SalesInvoice)
            .HasForeignKey(sil => sil.SalesInvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(si => si.SalesInvoiceTaxes)
            .WithOne(sit => sit.SalesInvoice)
            .HasForeignKey(sit => sit.SalesInvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(si => si.SalesInvoiceDiscounts)
            .WithOne(sid => sid.SalesInvoice)
            .HasForeignKey(sid => sid.SalesInvoiceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

public class SalesInvoiceLineConfiguration : IEntityTypeConfiguration<SalesInvoiceLine>
{
    public void Configure(EntityTypeBuilder<SalesInvoiceLine> builder)
    {
        builder.HasKey(sil => sil.Id);

        builder.Property(sil => sil.Quantity)
            .HasColumnType("decimal(18,4)");

        builder.Property(sil => sil.UnitPrice)
            .HasColumnType("decimal(18,4)");

        builder.Property(sil => sil.LineTotal)
            .HasColumnType("decimal(18,4)");

        builder.Property(sil => sil.Notes)
            .HasMaxLength(500);

        builder.Property(sil => sil.BatchNumber)
            .HasMaxLength(50);

        builder.Property(sil => sil.SerialNumber)
            .HasMaxLength(50);

        // Relationships
        builder.HasOne(sil => sil.Item)
            .WithMany(i => i.SalesInvoiceLines)
            .HasForeignKey(sil => sil.ItemId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(sil => sil.Unit)
            .WithMany(u => u.SalesInvoiceLines)
            .HasForeignKey(sil => sil.UnitId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(sil => sil.SalesInvoiceLineTaxes)
            .WithOne(silt => silt.SalesInvoiceLine)
            .HasForeignKey(silt => silt.SalesInvoiceLineId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(sil => sil.SalesInvoiceLineDiscounts)
            .WithOne(sild => sild.SalesInvoiceLine)
            .HasForeignKey(sild => sild.SalesInvoiceLineId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
