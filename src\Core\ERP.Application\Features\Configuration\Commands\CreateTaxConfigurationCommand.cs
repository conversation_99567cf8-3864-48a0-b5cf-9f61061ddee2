using AutoMapper;
using ERP.Application.Common.Interfaces;
using ERP.Domain.Entities.Configuration;
using ERP.DTOs.Configuration;
using ERP.Shared.Extensions;
using ERP.Shared.Models;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace ERP.Application.Features.Configuration.Commands;

public class CreateTaxConfigurationCommand : CreateTaxConfigurationRequest, IRequest<ApiResponse<TaxConfigurationDto>>
{
}

public class CreateTaxConfigurationCommandHandler : IRequestHandler<CreateTaxConfigurationCommand, ApiResponse<TaxConfigurationDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CreateTaxConfigurationCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<ApiResponse<TaxConfigurationDto>> Handle(CreateTaxConfigurationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var companyId = _httpContextAccessor.HttpContext?.User.GetCompanyId() ?? Guid.Empty;
            var userId = _httpContextAccessor.HttpContext?.User.GetUserId() ?? Guid.Empty;

            if (companyId == Guid.Empty)
            {
                return ApiResponse<TaxConfigurationDto>.ErrorResult("Company not found");
            }

            // Check if tax code already exists
            var existingTax = await _unitOfWork.TaxConfigurations.FirstOrDefaultAsync(
                t => t.TaxCode == request.TaxCode && t.CompanyId == companyId && !t.IsDeleted,
                cancellationToken);

            if (existingTax != null)
            {
                return ApiResponse<TaxConfigurationDto>.ErrorResult("Tax code already exists");
            }

            var taxConfiguration = _mapper.Map<TaxConfiguration>(request);
            taxConfiguration.CompanyId = companyId;
            taxConfiguration.CreatedBy = userId;

            // If this is set as default, remove default from others
            if (request.IsDefault)
            {
                var defaultTaxes = await _unitOfWork.TaxConfigurations.FindAsync(
                    t => t.IsDefault && t.CompanyId == companyId && !t.IsDeleted,
                    cancellationToken);

                foreach (var defaultTax in defaultTaxes)
                {
                    defaultTax.IsDefault = false;
                    defaultTax.UpdatedBy = userId;
                    defaultTax.UpdatedAt = DateTime.UtcNow;
                    await _unitOfWork.TaxConfigurations.UpdateAsync(defaultTax, cancellationToken);
                }
            }

            var createdTax = await _unitOfWork.TaxConfigurations.AddAsync(taxConfiguration, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var result = _mapper.Map<TaxConfigurationDto>(createdTax);
            return ApiResponse<TaxConfigurationDto>.SuccessResult(result, "Tax configuration created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<TaxConfigurationDto>.ErrorResult($"Error creating tax configuration: {ex.Message}");
        }
    }
}
