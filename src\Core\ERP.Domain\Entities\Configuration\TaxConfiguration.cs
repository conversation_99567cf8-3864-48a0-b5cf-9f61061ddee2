using ERP.Domain.Common;
using ERP.Domain.Enums;

namespace ERP.Domain.Entities.Configuration;

public class TaxConfiguration : BaseEntity
{
    public string TaxCode { get; set; } = string.Empty;
    public string TaxNameEn { get; set; } = string.Empty;
    public string TaxNameAr { get; set; } = string.Empty;
    public decimal TaxRate { get; set; }
    public TaxType TaxType { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsDefault { get; set; } = false;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public Company.Company Company { get; set; } = null!;
    public ICollection<Inventory.ItemTax> ItemTaxes { get; set; } = new List<Inventory.ItemTax>();
    public ICollection<Sales.SalesInvoiceTax> SalesInvoiceTaxes { get; set; } = new List<Sales.SalesInvoiceTax>();
    public ICollection<Sales.SalesInvoiceLineTax> SalesInvoiceLineTaxes { get; set; } = new List<Sales.SalesInvoiceLineTax>();
}
