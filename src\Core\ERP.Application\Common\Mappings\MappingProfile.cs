using AutoMapper;
using ERP.Domain.Entities.Configuration;
using ERP.Domain.Entities.Inventory;
using ERP.DTOs.Configuration;
using ERP.DTOs.Inventory;

namespace ERP.Application.Common.Mappings;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // Tax Configuration Mappings
        CreateMap<TaxConfiguration, TaxConfigurationDto>();
        CreateMap<CreateTaxConfigurationRequest, TaxConfiguration>();
        CreateMap<UpdateTaxConfigurationRequest, TaxConfiguration>();
        
        // Discount Configuration Mappings
        CreateMap<DiscountConfiguration, DiscountConfigurationDto>();
        CreateMap<CreateDiscountConfigurationRequest, DiscountConfiguration>();
        CreateMap<UpdateDiscountConfigurationRequest, DiscountConfiguration>();
        
        // Item Mappings
        CreateMap<Item, ItemDto>()
            .ForMember(dest => dest.Taxes, opt => opt.MapFrom(src => src.ItemTaxes.Select(it => new ItemTaxDto
            {
                Id = it.TaxConfiguration.Id,
                TaxCode = it.TaxConfiguration.TaxCode,
                TaxNameEn = it.TaxConfiguration.TaxNameEn,
                TaxNameAr = it.TaxConfiguration.TaxNameAr,
                TaxRate = it.TaxConfiguration.TaxRate,
                IsApplicable = it.IsApplicable
            })))
            .ForMember(dest => dest.Discounts, opt => opt.MapFrom(src => src.ItemDiscounts.Select(id => new ItemDiscountDto
            {
                Id = id.DiscountConfiguration.Id,
                DiscountCode = id.DiscountConfiguration.DiscountCode,
                DiscountNameEn = id.DiscountConfiguration.DiscountNameEn,
                DiscountNameAr = id.DiscountConfiguration.DiscountNameAr,
                DiscountValue = id.DiscountConfiguration.DiscountValue,
                IsApplicable = id.IsApplicable
            })))
            .ForMember(dest => dest.Units, opt => opt.MapFrom(src => src.ItemUnits));
            
        CreateMap<CreateItemRequest, Item>();
        
        // Supporting Entity Mappings
        CreateMap<ItemCategory, ItemCategoryDto>();
        CreateMap<ItemGroup, ItemGroupDto>();
        CreateMap<Unit, UnitDto>();
        CreateMap<Brand, BrandDto>();
        CreateMap<Manufacturer, ManufacturerDto>();
        CreateMap<ItemImage, ItemImageDto>();
        CreateMap<ItemUnit, ItemUnitDto>();
        
        // Item Tax and Discount Mappings
        CreateMap<CreateItemTaxRequest, ItemTax>();
        CreateMap<CreateItemDiscountRequest, ItemDiscount>();
        CreateMap<CreateItemUnitRequest, ItemUnit>();
    }
}
