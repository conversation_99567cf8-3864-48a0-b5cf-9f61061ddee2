using ERP.Domain.Common;

namespace ERP.Domain.Entities.Sales;

public class SalesInvoiceLine : BaseEntity
{
    public Guid SalesInvoiceId { get; set; }
    public Guid ItemId { get; set; }
    public decimal Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal LineTotal { get; set; }
    public string Notes { get; set; } = string.Empty;
    public Guid UnitId { get; set; }
    public string BatchNumber { get; set; } = string.Empty;
    public string SerialNumber { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    
    // Navigation Properties
    public SalesInvoice SalesInvoice { get; set; } = null!;
    public Inventory.Item Item { get; set; } = null!;
    public Inventory.Unit Unit { get; set; } = null!;
    public ICollection<SalesInvoiceLineTax> SalesInvoiceLineTaxes { get; set; } = new List<SalesInvoiceLineTax>();
    public ICollection<SalesInvoiceLineDiscount> SalesInvoiceLineDiscounts { get; set; } = new List<SalesInvoiceLineDiscount>();
}
