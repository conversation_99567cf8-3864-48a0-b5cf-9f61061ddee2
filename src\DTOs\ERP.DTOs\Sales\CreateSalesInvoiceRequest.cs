using ERP.Domain.Enums;

namespace ERP.DTOs.Sales;

public class CreateSalesInvoiceRequest
{
    public Guid CustomerId { get; set; }
    public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;
    public DateTime DueDate { get; set; }
    public string Reference { get; set; } = string.Empty;
    public string CurrencyCode { get; set; } = "SAR";
    public decimal ExchangeRate { get; set; } = 1;
    public string Notes { get; set; } = string.Empty;
    
    public List<CreateSalesInvoiceLineRequest> Lines { get; set; } = new();
    public List<CreateSalesInvoiceDiscountRequest> Discounts { get; set; } = new();
}

public class CreateSalesInvoiceLineRequest
{
    public Guid ItemId { get; set; }
    public decimal Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public string Notes { get; set; } = string.Empty;
    public Guid UnitId { get; set; }
    public string BatchNumber { get; set; } = string.Empty;
    public string SerialNumber { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    
    public List<CreateSalesInvoiceLineDiscountRequest> Discounts { get; set; } = new();
}

public class CreateSalesInvoiceDiscountRequest
{
    public Guid? DiscountConfigurationId { get; set; }
    public DiscountType DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
}

public class CreateSalesInvoiceLineDiscountRequest
{
    public Guid? DiscountConfigurationId { get; set; }
    public DiscountType DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
}
