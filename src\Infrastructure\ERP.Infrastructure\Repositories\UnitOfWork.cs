using ERP.Application.Common.Interfaces;
using ERP.Domain.Entities.Audit;
using ERP.Domain.Entities.Company;
using ERP.Domain.Entities.Configuration;
using ERP.Domain.Entities.Customers;
using ERP.Domain.Entities.Inventory;
using ERP.Domain.Entities.Sales;
using ERP.Infrastructure.Data;
using Microsoft.EntityFrameworkCore.Storage;

namespace ERP.Infrastructure.Repositories;

public class UnitOfWork : IUnitOfWork
{
    private readonly ApplicationDbContext _context;
    private IDbContextTransaction? _transaction;

    // Configuration
    private IRepository<TaxConfiguration>? _taxConfigurations;
    private IRepository<DiscountConfiguration>? _discountConfigurations;

    // Company
    private IRepository<Company>? _companies;
    private IRepository<Branch>? _branches;

    // Inventory
    private IRepository<Item>? _items;
    private IRepository<ItemCategory>? _itemCategories;
    private IRepository<ItemGroup>? _itemGroups;
    private IRepository<Unit>? _units;
    private IRepository<Brand>? _brands;
    private IRepository<Manufacturer>? _manufacturers;
    private IRepository<ItemTax>? _itemTaxes;
    private IRepository<ItemDiscount>? _itemDiscounts;
    private IRepository<ItemUnit>? _itemUnits;
    private IRepository<ItemImage>? _itemImages;
    private IRepository<ItemStock>? _itemStocks;

    // Customers
    private IRepository<Customer>? _customers;

    // Sales
    private IRepository<SalesInvoice>? _salesInvoices;
    private IRepository<SalesInvoiceLine>? _salesInvoiceLines;
    private IRepository<SalesInvoiceTax>? _salesInvoiceTaxes;
    private IRepository<SalesInvoiceLineTax>? _salesInvoiceLineTaxes;
    private IRepository<SalesInvoiceDiscount>? _salesInvoiceDiscounts;
    private IRepository<SalesInvoiceLineDiscount>? _salesInvoiceLineDiscounts;

    // Audit
    private IRepository<AuditLog>? _auditLogs;

    public UnitOfWork(ApplicationDbContext context)
    {
        _context = context;
    }

    // Configuration
    public IRepository<TaxConfiguration> TaxConfigurations => 
        _taxConfigurations ??= new Repository<TaxConfiguration>(_context);
    public IRepository<DiscountConfiguration> DiscountConfigurations => 
        _discountConfigurations ??= new Repository<DiscountConfiguration>(_context);

    // Company
    public IRepository<Company> Companies => 
        _companies ??= new Repository<Company>(_context);
    public IRepository<Branch> Branches => 
        _branches ??= new Repository<Branch>(_context);

    // Inventory
    public IRepository<Item> Items => 
        _items ??= new Repository<Item>(_context);
    public IRepository<ItemCategory> ItemCategories => 
        _itemCategories ??= new Repository<ItemCategory>(_context);
    public IRepository<ItemGroup> ItemGroups => 
        _itemGroups ??= new Repository<ItemGroup>(_context);
    public IRepository<Unit> Units => 
        _units ??= new Repository<Unit>(_context);
    public IRepository<Brand> Brands => 
        _brands ??= new Repository<Brand>(_context);
    public IRepository<Manufacturer> Manufacturers => 
        _manufacturers ??= new Repository<Manufacturer>(_context);
    public IRepository<ItemTax> ItemTaxes => 
        _itemTaxes ??= new Repository<ItemTax>(_context);
    public IRepository<ItemDiscount> ItemDiscounts => 
        _itemDiscounts ??= new Repository<ItemDiscount>(_context);
    public IRepository<ItemUnit> ItemUnits => 
        _itemUnits ??= new Repository<ItemUnit>(_context);
    public IRepository<ItemImage> ItemImages => 
        _itemImages ??= new Repository<ItemImage>(_context);
    public IRepository<ItemStock> ItemStocks => 
        _itemStocks ??= new Repository<ItemStock>(_context);

    // Customers
    public IRepository<Customer> Customers => 
        _customers ??= new Repository<Customer>(_context);

    // Sales
    public IRepository<SalesInvoice> SalesInvoices => 
        _salesInvoices ??= new Repository<SalesInvoice>(_context);
    public IRepository<SalesInvoiceLine> SalesInvoiceLines => 
        _salesInvoiceLines ??= new Repository<SalesInvoiceLine>(_context);
    public IRepository<SalesInvoiceTax> SalesInvoiceTaxes => 
        _salesInvoiceTaxes ??= new Repository<SalesInvoiceTax>(_context);
    public IRepository<SalesInvoiceLineTax> SalesInvoiceLineTaxes => 
        _salesInvoiceLineTaxes ??= new Repository<SalesInvoiceLineTax>(_context);
    public IRepository<SalesInvoiceDiscount> SalesInvoiceDiscounts => 
        _salesInvoiceDiscounts ??= new Repository<SalesInvoiceDiscount>(_context);
    public IRepository<SalesInvoiceLineDiscount> SalesInvoiceLineDiscounts => 
        _salesInvoiceLineDiscounts ??= new Repository<SalesInvoiceLineDiscount>(_context);

    // Audit
    public IRepository<AuditLog> AuditLogs => 
        _auditLogs ??= new Repository<AuditLog>(_context);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
