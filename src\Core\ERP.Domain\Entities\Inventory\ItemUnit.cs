using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class ItemUnit : BaseEntity
{
    public Guid ItemId { get; set; }
    public Guid UnitId { get; set; }
    public decimal ConversionFactor { get; set; } = 1;
    public decimal Price { get; set; }
    public bool IsDefault { get; set; } = false;
    
    // Navigation Properties
    public Item Item { get; set; } = null!;
    public Unit Unit { get; set; } = null!;
}
