using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using ERP.DTOs.Authentication;
using ERP.Identity.Models;
using ERP.Shared.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace ERP.Identity.Services;

public interface IAuthenticationService
{
    Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request);
    Task<ApiResponse<string>> RefreshTokenAsync(string refreshToken);
    Task<ApiResponse> LogoutAsync(string userId);
}

public class AuthenticationService : IAuthenticationService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly IConfiguration _configuration;

    public AuthenticationService(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        IConfiguration configuration)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _configuration = configuration;
    }

    public async Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request)
    {
        try
        {
            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user == null)
            {
                return ApiResponse<LoginResponse>.ErrorResult("Invalid email or password");
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, false);
            if (!result.Succeeded)
            {
                return ApiResponse<LoginResponse>.ErrorResult("Invalid email or password");
            }

            if (!user.IsActive)
            {
                return ApiResponse<LoginResponse>.ErrorResult("User account is inactive");
            }

            var accessToken = await GenerateAccessTokenAsync(user);
            var refreshToken = GenerateRefreshToken();

            // Store refresh token (in a real application, you'd store this in the database)
            // For now, we'll just return it

            var response = new LoginResponse
            {
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                ExpiresIn = int.Parse(_configuration["JWT:ExpiryInMinutes"] ?? "15") * 60,
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email!,
                    FullName = user.FullName,
                    CompanyId = user.CompanyId,
                    CompanyName = "Default Company" // You'd get this from the database
                }
            };

            return ApiResponse<LoginResponse>.SuccessResult(response, "Login successful");
        }
        catch (Exception ex)
        {
            return ApiResponse<LoginResponse>.ErrorResult($"Login failed: {ex.Message}");
        }
    }

    public async Task<ApiResponse<string>> RefreshTokenAsync(string refreshToken)
    {
        // In a real application, you'd validate the refresh token against the database
        // and generate a new access token
        await Task.CompletedTask;
        return ApiResponse<string>.ErrorResult("Refresh token functionality not implemented");
    }

    public async Task<ApiResponse> LogoutAsync(string userId)
    {
        try
        {
            // In a real application, you'd invalidate the refresh token in the database
            await Task.CompletedTask;
            return ApiResponse.SuccessResult("Logout successful");
        }
        catch (Exception ex)
        {
            return ApiResponse.ErrorResult($"Logout failed: {ex.Message}");
        }
    }

    private async Task<string> GenerateAccessTokenAsync(ApplicationUser user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_configuration["JWT:Secret"] ?? throw new InvalidOperationException("JWT Secret not configured"));

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Name, user.FullName),
            new(ClaimTypes.Email, user.Email!),
            new("CompanyId", user.CompanyId.ToString())
        };

        // Add role claims
        var roles = await _userManager.GetRolesAsync(user);
        claims.AddRange(roles.Select(role => new Claim(ClaimTypes.Role, role)));

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(int.Parse(_configuration["JWT:ExpiryInMinutes"] ?? "15")),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
            Issuer = _configuration["JWT:Issuer"],
            Audience = _configuration["JWT:Audience"]
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    private static string GenerateRefreshToken()
    {
        return Guid.NewGuid().ToString();
    }
}
