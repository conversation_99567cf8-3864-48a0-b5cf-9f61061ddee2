using ERP.Domain.Enums;

namespace ERP.DTOs.Sales;

public class SalesInvoiceDto
{
    public Guid Id { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public DateTime InvoiceDate { get; set; }
    public DateTime DueDate { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string Reference { get; set; } = string.Empty;
    public decimal SubTotal { get; set; }
    public decimal TotalTaxAmount { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public string CurrencyCode { get; set; } = string.Empty;
    public decimal ExchangeRate { get; set; }
    public InvoiceStatus Status { get; set; }
    public string Notes { get; set; } = string.Empty;
    
    public List<SalesInvoiceLineDto> Lines { get; set; } = new();
    public List<SalesInvoiceTaxDto> Taxes { get; set; } = new();
    public List<SalesInvoiceDiscountDto> Discounts { get; set; } = new();
}

public class SalesInvoiceLineDto
{
    public Guid Id { get; set; }
    public Guid ItemId { get; set; }
    public string ItemCode { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal LineTotal { get; set; }
    public string Notes { get; set; } = string.Empty;
    public Guid UnitId { get; set; }
    public string UnitName { get; set; } = string.Empty;
    public string BatchNumber { get; set; } = string.Empty;
    public string SerialNumber { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    
    public List<SalesInvoiceLineTaxDto> Taxes { get; set; } = new();
    public List<SalesInvoiceLineDiscountDto> Discounts { get; set; } = new();
}

public class SalesInvoiceTaxDto
{
    public Guid Id { get; set; }
    public Guid TaxConfigurationId { get; set; }
    public string TaxCode { get; set; } = string.Empty;
    public string TaxName { get; set; } = string.Empty;
    public decimal TaxRate { get; set; }
    public decimal TaxableAmount { get; set; }
    public decimal TaxAmount { get; set; }
}

public class SalesInvoiceLineTaxDto
{
    public Guid Id { get; set; }
    public Guid TaxConfigurationId { get; set; }
    public string TaxCode { get; set; } = string.Empty;
    public string TaxName { get; set; } = string.Empty;
    public decimal TaxRate { get; set; }
    public decimal TaxableAmount { get; set; }
    public decimal TaxAmount { get; set; }
}

public class SalesInvoiceDiscountDto
{
    public Guid Id { get; set; }
    public Guid? DiscountConfigurationId { get; set; }
    public string DiscountCode { get; set; } = string.Empty;
    public string DiscountName { get; set; } = string.Empty;
    public DiscountType DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
    public decimal DiscountAmount { get; set; }
}

public class SalesInvoiceLineDiscountDto
{
    public Guid Id { get; set; }
    public Guid? DiscountConfigurationId { get; set; }
    public string DiscountCode { get; set; } = string.Empty;
    public string DiscountName { get; set; } = string.Empty;
    public DiscountType DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
    public decimal DiscountAmount { get; set; }
}
