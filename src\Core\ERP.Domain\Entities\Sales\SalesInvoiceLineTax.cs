using ERP.Domain.Common;

namespace ERP.Domain.Entities.Sales;

public class SalesInvoiceLineTax : BaseEntity
{
    public Guid SalesInvoiceLineId { get; set; }
    public Guid TaxConfigurationId { get; set; }
    public decimal TaxableAmount { get; set; }
    public decimal TaxAmount { get; set; }
    
    // Navigation Properties
    public SalesInvoiceLine SalesInvoiceLine { get; set; } = null!;
    public Configuration.TaxConfiguration TaxConfiguration { get; set; } = null!;
}
