using ERP.Domain.Common;
using ERP.Domain.Enums;

namespace ERP.Domain.Entities.Inventory;

public class Item : BaseEntity
{
    public string ItemCode { get; set; } = string.Empty;
    public string ItemNameEn { get; set; } = string.Empty;
    public string ItemNameAr { get; set; } = string.Empty;
    public string DescriptionEn { get; set; } = string.Empty;
    public string DescriptionAr { get; set; } = string.Empty;
    public string Barcode { get; set; } = string.Empty;
    public string QRCode { get; set; } = string.Empty;
    public string SKU { get; set; } = string.Empty;
    public Guid ItemCategoryId { get; set; }
    public Guid ItemGroupId { get; set; }
    public Guid BaseUnitId { get; set; }
    public Guid? BrandId { get; set; }
    public Guid? ManufacturerId { get; set; }
    public decimal CostPrice { get; set; }
    public decimal SellingPrice { get; set; }
    public decimal WholesalePrice { get; set; }
    public decimal MinSellingPrice { get; set; }
    public decimal Weight { get; set; }
    public string Dimensions { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public string BatchNumber { get; set; } = string.Empty;
    public string SerialNumber { get; set; } = string.Empty;
    public decimal MinStockLevel { get; set; }
    public decimal MaxStockLevel { get; set; }
    public decimal ReorderLevel { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsInventoryItem { get; set; } = true;
    public bool IsSerialized { get; set; } = false;
    public bool HasExpiryDate { get; set; } = false;
    public ItemType ItemType { get; set; }
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public ItemCategory ItemCategory { get; set; } = null!;
    public ItemGroup ItemGroup { get; set; } = null!;
    public Unit BaseUnit { get; set; } = null!;
    public Brand? Brand { get; set; }
    public Manufacturer? Manufacturer { get; set; }
    public Company.Company Company { get; set; } = null!;
    public ICollection<ItemTax> ItemTaxes { get; set; } = new List<ItemTax>();
    public ICollection<ItemDiscount> ItemDiscounts { get; set; } = new List<ItemDiscount>();
    public ICollection<ItemUnit> ItemUnits { get; set; } = new List<ItemUnit>();
    public ICollection<ItemImage> ItemImages { get; set; } = new List<ItemImage>();
    public ICollection<ItemStock> ItemStocks { get; set; } = new List<ItemStock>();
    public ICollection<Sales.SalesInvoiceLine> SalesInvoiceLines { get; set; } = new List<Sales.SalesInvoiceLine>();
}
