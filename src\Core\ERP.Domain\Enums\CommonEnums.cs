namespace ERP.Domain.Enums;

public enum TaxType
{
    VAT = 1,
    SalesTax = 2,
    ServiceTax = 3,
    ExciseTax = 4,
    CustomDuty = 5
}

public enum DiscountType
{
    Percentage = 1,
    FixedAmount = 2
}

public enum ItemType
{
    Product = 1,
    Service = 2,
    Bundle = 3,
    Digital = 4
}

public enum InvoiceStatus
{
    Draft = 1,
    Pending = 2,
    Approved = 3,
    Sent = 4,
    Paid = 5,
    Overdue = 6,
    Cancelled = 7,
    Refunded = 8
}

public enum PaymentStatus
{
    Pending = 1,
    Partial = 2,
    Paid = 3,
    Overdue = 4,
    Cancelled = 5
}

public enum DocumentType
{
    SalesInvoice = 1,
    PurchaseInvoice = 2,
    SalesOrder = 3,
    PurchaseOrder = 4,
    DeliveryNote = 5,
    Receipt = 6,
    Payment = 7,
    CreditNote = 8,
    DebitNote = 9
}
