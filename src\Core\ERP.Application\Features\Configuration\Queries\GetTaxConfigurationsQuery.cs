using AutoMapper;
using ERP.Application.Common.Interfaces;
using ERP.DTOs.Configuration;
using ERP.Shared.Extensions;
using ERP.Shared.Models;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace ERP.Application.Features.Configuration.Queries;

public class GetTaxConfigurationsQuery : IRequest<ApiResponse<List<TaxConfigurationDto>>>
{
    public bool? IsActive { get; set; }
}

public class GetTaxConfigurationsQueryHandler : IRequestHandler<GetTaxConfigurationsQuery, ApiResponse<List<TaxConfigurationDto>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetTaxConfigurationsQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<ApiResponse<List<TaxConfigurationDto>>> Handle(GetTaxConfigurationsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var companyId = _httpContextAccessor.HttpContext?.User.GetCompanyId() ?? Guid.Empty;

            if (companyId == Guid.Empty)
            {
                return ApiResponse<List<TaxConfigurationDto>>.ErrorResult("Company not found");
            }

            var taxConfigurations = await _unitOfWork.TaxConfigurations.FindAsync(
                t => t.CompanyId == companyId && 
                     !t.IsDeleted &&
                     (request.IsActive == null || t.IsActive == request.IsActive),
                cancellationToken);

            var result = _mapper.Map<List<TaxConfigurationDto>>(taxConfigurations.OrderBy(t => t.TaxCode));
            return ApiResponse<List<TaxConfigurationDto>>.SuccessResult(result);
        }
        catch (Exception ex)
        {
            return ApiResponse<List<TaxConfigurationDto>>.ErrorResult($"Error retrieving tax configurations: {ex.Message}");
        }
    }
}
