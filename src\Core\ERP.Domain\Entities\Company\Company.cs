using ERP.Domain.Common;

namespace ERP.Domain.Entities.Company;

public class Company : BaseEntity
{
    public string CompanyCode { get; set; } = string.Empty;
    public string CompanyNameEn { get; set; } = string.Empty;
    public string CompanyNameAr { get; set; } = string.Empty;
    public string TaxNumber { get; set; } = string.Empty;
    public string CommercialRegistration { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string LogoPath { get; set; } = string.Empty;
    public string BaseCurrency { get; set; } = "SAR";
    public bool IsActive { get; set; } = true;
    
    // Navigation Properties
    public ICollection<Branch> Branches { get; set; } = new List<Branch>();
    public ICollection<Configuration.TaxConfiguration> TaxConfigurations { get; set; } = new List<Configuration.TaxConfiguration>();
    public ICollection<Configuration.DiscountConfiguration> DiscountConfigurations { get; set; } = new List<Configuration.DiscountConfiguration>();
    public ICollection<Inventory.Item> Items { get; set; } = new List<Inventory.Item>();
    public ICollection<Sales.SalesInvoice> SalesInvoices { get; set; } = new List<Sales.SalesInvoice>();
}
