using ERP.Domain.Entities.Audit;
using ERP.Domain.Entities.Company;
using ERP.Domain.Entities.Configuration;
using ERP.Domain.Entities.Customers;
using ERP.Domain.Entities.Inventory;
using ERP.Domain.Entities.Sales;
using Microsoft.EntityFrameworkCore;

namespace ERP.Infrastructure.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    // Configuration
    public DbSet<TaxConfiguration> TaxConfigurations { get; set; }
    public DbSet<DiscountConfiguration> DiscountConfigurations { get; set; }

    // Company
    public DbSet<Company> Companies { get; set; }
    public DbSet<Branch> Branches { get; set; }

    // Inventory
    public DbSet<Item> Items { get; set; }
    public DbSet<ItemCategory> ItemCategories { get; set; }
    public DbSet<ItemGroup> ItemGroups { get; set; }
    public DbSet<Unit> Units { get; set; }
    public DbSet<Brand> Brands { get; set; }
    public DbSet<Manufacturer> Manufacturers { get; set; }
    public DbSet<ItemTax> ItemTaxes { get; set; }
    public DbSet<ItemDiscount> ItemDiscounts { get; set; }
    public DbSet<ItemUnit> ItemUnits { get; set; }
    public DbSet<ItemImage> ItemImages { get; set; }
    public DbSet<ItemStock> ItemStocks { get; set; }

    // Customers
    public DbSet<Customer> Customers { get; set; }

    // Sales
    public DbSet<SalesInvoice> SalesInvoices { get; set; }
    public DbSet<SalesInvoiceLine> SalesInvoiceLines { get; set; }
    public DbSet<SalesInvoiceTax> SalesInvoiceTaxes { get; set; }
    public DbSet<SalesInvoiceLineTax> SalesInvoiceLineTaxes { get; set; }
    public DbSet<SalesInvoiceDiscount> SalesInvoiceDiscounts { get; set; }
    public DbSet<SalesInvoiceLineDiscount> SalesInvoiceLineDiscounts { get; set; }

    // Audit
    public DbSet<AuditLog> AuditLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all configurations from the assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);

        // Global query filters for soft delete
        modelBuilder.Entity<Company>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Branch>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<TaxConfiguration>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<DiscountConfiguration>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Item>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<ItemCategory>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<ItemGroup>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Unit>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Brand>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Manufacturer>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Customer>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<SalesInvoice>().HasQueryFilter(e => !e.IsDeleted);
    }
}
