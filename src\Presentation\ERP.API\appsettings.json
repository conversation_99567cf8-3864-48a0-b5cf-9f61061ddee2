{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ERPDatabase;Trusted_Connection=true;MultipleActiveResultSets=true", "IdentityConnection": "Server=(localdb)\\mssqllocaldb;Database=ERPIdentityDatabase;Trusted_Connection=true;MultipleActiveResultSets=true"}, "JWT": {"Secret": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "ERPSystem", "Audience": "ERPUsers", "ExpiryInMinutes": "15"}, "Serilog": {"Using": ["Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "logs/erp-api-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}