using ERP.Domain.Entities.Audit;
using ERP.Domain.Entities.Company;
using ERP.Domain.Entities.Configuration;
using ERP.Domain.Entities.Customers;
using ERP.Domain.Entities.Inventory;
using ERP.Domain.Entities.Sales;

namespace ERP.Application.Common.Interfaces;

public interface IUnitOfWork : IDisposable
{
    // Configuration
    IRepository<TaxConfiguration> TaxConfigurations { get; }
    IRepository<DiscountConfiguration> DiscountConfigurations { get; }
    
    // Company
    IRepository<Company> Companies { get; }
    IRepository<Branch> Branches { get; }
    
    // Inventory
    IRepository<Item> Items { get; }
    IRepository<ItemCategory> ItemCategories { get; }
    IRepository<ItemGroup> ItemGroups { get; }
    IRepository<Unit> Units { get; }
    IRepository<Brand> Brands { get; }
    IRepository<Manufacturer> Manufacturers { get; }
    IRepository<ItemTax> ItemTaxes { get; }
    IRepository<ItemDiscount> ItemDiscounts { get; }
    IRepository<ItemUnit> ItemUnits { get; }
    IRepository<ItemImage> ItemImages { get; }
    IRepository<ItemStock> ItemStocks { get; }
    
    // Customers
    IRepository<Customer> Customers { get; }
    
    // Sales
    IRepository<SalesInvoice> SalesInvoices { get; }
    IRepository<SalesInvoiceLine> SalesInvoiceLines { get; }
    IRepository<SalesInvoiceTax> SalesInvoiceTaxes { get; }
    IRepository<SalesInvoiceLineTax> SalesInvoiceLineTaxes { get; }
    IRepository<SalesInvoiceDiscount> SalesInvoiceDiscounts { get; }
    IRepository<SalesInvoiceLineDiscount> SalesInvoiceLineDiscounts { get; }
    
    // Audit
    IRepository<AuditLog> AuditLogs { get; }
    
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
