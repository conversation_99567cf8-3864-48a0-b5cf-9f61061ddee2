{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Infrastructure\\ERP.Infrastructure.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Application\\ERP.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Application\\ERP.Application.csproj", "projectName": "ERP.Application", "projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Application\\ERP.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj"}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Shared\\ERP.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Shared\\ERP.Shared.csproj"}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\DTOs\\ERP.DTOs\\ERP.DTOs.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\DTOs\\ERP.DTOs\\ERP.DTOs.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.1, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.1, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Scrutor": {"target": "Package", "version": "[4.2.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj", "projectName": "ERP.Domain", "projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Shared\\ERP.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Shared\\ERP.Shared.csproj", "projectName": "ERP.Shared", "projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Shared\\ERP.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\DTOs\\ERP.DTOs\\ERP.DTOs.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\DTOs\\ERP.DTOs\\ERP.DTOs.csproj", "projectName": "ERP.DTOs", "projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\DTOs\\ERP.DTOs\\ERP.DTOs.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\DTOs\\ERP.DTOs\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Identity\\ERP.Identity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Identity\\ERP.Identity.csproj", "projectName": "ERP.Identity", "projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Identity\\ERP.Identity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Identity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Shared\\ERP.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Shared\\ERP.Shared.csproj"}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\DTOs\\ERP.DTOs\\ERP.DTOs.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\DTOs\\ERP.DTOs\\ERP.DTOs.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Infrastructure\\ERP.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Infrastructure\\ERP.Infrastructure.csproj", "projectName": "ERP.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Infrastructure\\ERP.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Application\\ERP.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Application\\ERP.Application.csproj"}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Core\\ERP.Domain\\ERP.Domain.csproj"}, "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Identity\\ERP.Identity.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\New folder (2)\\src\\Infrastructure\\ERP.Identity\\ERP.Identity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}