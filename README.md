# ERP System API

A comprehensive ERP System API built with .NET 8, featuring clean architecture, multi-language support (Arabic/English), JWT authentication, and comprehensive business modules.

## Features

- **Clean Architecture** with modular design
- **Multi-language support** (Arabic/English) with JSON localization
- **JWT Authentication** with Microsoft Identity
- **Comprehensive Audit Middleware** for all operations
- **Tax and Discount Configuration** with flexible application
- **Enhanced Item Management** with categories, brands, manufacturers
- **Sales Invoice Management** with detailed tax and discount calculations
- **RESTful API** with Swagger documentation
- **Entity Framework Core** with SQL Server
- **CQRS Pattern** using MediatR
- **Repository Pattern** with Unit of Work
- **Automatic Service Registration** using Scrutor

## Technology Stack

- **.NET 8** - Framework
- **Entity Framework Core** - ORM
- **Microsoft Identity** - Authentication
- **JWT Bearer** - Authorization
- **MediatR** - CQRS implementation
- **AutoMapper** - Object mapping
- **FluentValidation** - Input validation
- **Scrutor** - Service registration
- **Serilog** - Logging
- **Swagger/OpenAPI** - API documentation
- **SQL Server** - Database

## Project Structure

```
ERP.Solution/
├── src/
│   ├── Core/
│   │   ├── ERP.Domain/              # Entities and business rules
│   │   ├── ERP.Application/         # Use cases and services
│   │   └── ERP.Shared/             # Shared utilities
│   ├── Infrastructure/
│   │   ├── ERP.Infrastructure/      # Data access layer
│   │   └── ERP.Identity/           # Authentication service
│   ├── Presentation/
│   │   └── ERP.API/                # Controllers and middleware
│   └── DTOs/
│       └── ERP.DTOs/               # Data transfer objects
```

## Getting Started

### Prerequisites

- .NET 8 SDK
- SQL Server (LocalDB or full instance)
- Visual Studio 2022 or VS Code

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ERP.Solution
   ```

2. **Restore packages**
   ```bash
   dotnet restore
   ```

3. **Update connection strings**
   
   Edit `src/Presentation/ERP.API/appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Your-Database-Connection-String",
       "IdentityConnection": "Your-Identity-Database-Connection-String"
     }
   }
   ```

4. **Create and run migrations**
   ```bash
   # For main database
   dotnet ef migrations add InitialCreate --project src/Infrastructure/ERP.Infrastructure --startup-project src/Presentation/ERP.API --context ApplicationDbContext
   
   # For identity database
   dotnet ef migrations add InitialIdentityCreate --project src/Infrastructure/ERP.Identity --startup-project src/Presentation/ERP.API --context IdentityDbContext
   
   # Update databases
   dotnet ef database update --project src/Infrastructure/ERP.Infrastructure --startup-project src/Presentation/ERP.API --context ApplicationDbContext
   dotnet ef database update --project src/Infrastructure/ERP.Identity --startup-project src/Presentation/ERP.API --context IdentityDbContext
   ```

5. **Run the application**
   ```bash
   cd src/Presentation/ERP.API
   dotnet run
   ```

6. **Access the API**
   - API: `https://localhost:7000` or `http://localhost:5000`
   - Swagger UI: `https://localhost:7000/swagger`

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/logout` - User logout

### Configuration
- `GET /api/configuration/taxes` - Get tax configurations
- `POST /api/configuration/taxes` - Create tax configuration
- `POST /api/configuration/discounts` - Create discount configuration

### Inventory
- `GET /api/inventory/items` - Get paginated items
- `POST /api/inventory/items` - Create new item

## Sample Requests

### Login
```json
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

### Create Tax Configuration
```json
POST /api/configuration/taxes
{
  "taxCode": "VAT",
  "taxNameEn": "Value Added Tax",
  "taxNameAr": "ضريبة القيمة المضافة",
  "taxRate": 15.0,
  "taxType": 1,
  "isActive": true,
  "isDefault": true
}
```

### Create Item
```json
POST /api/inventory/items
{
  "itemCode": "ITM001",
  "itemNameEn": "Laptop Computer",
  "itemNameAr": "كمبيوتر محمول",
  "itemCategoryId": "guid-here",
  "itemGroupId": "guid-here",
  "baseUnitId": "guid-here",
  "costPrice": 2000.00,
  "sellingPrice": 2500.00,
  "itemType": 1,
  "itemTaxes": [
    {
      "taxConfigurationId": "guid-here",
      "isApplicable": true
    }
  ]
}
```

## Configuration

### JWT Settings
Configure JWT settings in `appsettings.json`:
```json
{
  "JWT": {
    "Secret": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "ERPSystem",
    "Audience": "ERPUsers",
    "ExpiryInMinutes": "15"
  }
}
```

### Localization
The system supports English and Arabic localization through JSON files:
- `Resources/en.json` - English translations
- `Resources/ar.json` - Arabic translations

## Development

### Adding New Features

1. **Domain Entities**: Add to `ERP.Domain/Entities`
2. **DTOs**: Add to `ERP.DTOs`
3. **Commands/Queries**: Add to `ERP.Application/Features`
4. **Controllers**: Add to `ERP.API/Controllers`
5. **Database Configuration**: Add to `ERP.Infrastructure/Data/Configurations`

### Running Tests
```bash
dotnet test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
