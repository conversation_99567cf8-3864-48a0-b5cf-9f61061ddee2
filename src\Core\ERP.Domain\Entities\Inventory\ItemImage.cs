using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class ItemImage : BaseEntity
{
    public Guid ItemId { get; set; }
    public string ImagePath { get; set; } = string.Empty;
    public string ImageName { get; set; } = string.Empty;
    public bool IsPrimary { get; set; } = false;
    public int SortOrder { get; set; } = 0;
    
    // Navigation Properties
    public Item Item { get; set; } = null!;
}
