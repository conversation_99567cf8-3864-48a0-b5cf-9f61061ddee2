using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class ItemCategory : BaseEntity
{
    public string CategoryCode { get; set; } = string.Empty;
    public string CategoryNameEn { get; set; } = string.Empty;
    public string CategoryNameAr { get; set; } = string.Empty;
    public string DescriptionEn { get; set; } = string.Empty;
    public string DescriptionAr { get; set; } = string.Empty;
    public Guid? ParentCategoryId { get; set; }
    public bool IsActive { get; set; } = true;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public ItemCategory? ParentCategory { get; set; }
    public Company.Company Company { get; set; } = null!;
    public ICollection<ItemCategory> SubCategories { get; set; } = new List<ItemCategory>();
    public ICollection<Item> Items { get; set; } = new List<Item>();
}
