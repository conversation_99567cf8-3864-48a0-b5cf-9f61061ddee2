using ERP.Domain.Common;
using ERP.Domain.Enums;

namespace ERP.Domain.Entities.Configuration;

public class DiscountConfiguration : BaseEntity
{
    public string DiscountCode { get; set; } = string.Empty;
    public string DiscountNameEn { get; set; } = string.Empty;
    public string DiscountNameAr { get; set; } = string.Empty;
    public DiscountType DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public decimal MinimumAmount { get; set; }
    public decimal MaximumDiscount { get; set; }
    public bool IsActive { get; set; } = true;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public Company.Company Company { get; set; } = null!;
    public ICollection<Inventory.ItemDiscount> ItemDiscounts { get; set; } = new List<Inventory.ItemDiscount>();
    public ICollection<Sales.SalesInvoiceDiscount> SalesInvoiceDiscounts { get; set; } = new List<Sales.SalesInvoiceDiscount>();
    public ICollection<Sales.SalesInvoiceLineDiscount> SalesInvoiceLineDiscounts { get; set; } = new List<Sales.SalesInvoiceLineDiscount>();
}
