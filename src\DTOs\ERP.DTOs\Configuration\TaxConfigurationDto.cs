using ERP.Domain.Enums;

namespace ERP.DTOs.Configuration;

public class TaxConfigurationDto
{
    public Guid Id { get; set; }
    public string TaxCode { get; set; } = string.Empty;
    public string TaxNameEn { get; set; } = string.Empty;
    public string TaxNameAr { get; set; } = string.Empty;
    public decimal TaxRate { get; set; }
    public TaxType TaxType { get; set; }
    public bool IsActive { get; set; }
    public bool IsDefault { get; set; }
    public Guid CompanyId { get; set; }
}

public class CreateTaxConfigurationRequest
{
    public string TaxCode { get; set; } = string.Empty;
    public string TaxNameEn { get; set; } = string.Empty;
    public string TaxNameAr { get; set; } = string.Empty;
    public decimal TaxRate { get; set; }
    public TaxType TaxType { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsDefault { get; set; } = false;
}

public class UpdateTaxConfigurationRequest : CreateTaxConfigurationRequest
{
    public Guid Id { get; set; }
}
