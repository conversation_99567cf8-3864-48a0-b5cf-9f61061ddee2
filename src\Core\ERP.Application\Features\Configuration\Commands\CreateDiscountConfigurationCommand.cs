using AutoMapper;
using ERP.Application.Common.Interfaces;
using ERP.Domain.Entities.Configuration;
using ERP.DTOs.Configuration;
using ERP.Shared.Extensions;
using ERP.Shared.Models;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace ERP.Application.Features.Configuration.Commands;

public class CreateDiscountConfigurationCommand : CreateDiscountConfigurationRequest, IRequest<ApiResponse<DiscountConfigurationDto>>
{
}

public class CreateDiscountConfigurationCommandHandler : IRequestHandler<CreateDiscountConfigurationCommand, ApiResponse<DiscountConfigurationDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CreateDiscountConfigurationCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<ApiResponse<DiscountConfigurationDto>> Handle(CreateDiscountConfigurationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var companyId = _httpContextAccessor.HttpContext?.User.GetCompanyId() ?? Guid.Empty;
            var userId = _httpContextAccessor.HttpContext?.User.GetUserId() ?? Guid.Empty;

            if (companyId == Guid.Empty)
            {
                return ApiResponse<DiscountConfigurationDto>.ErrorResult("Company not found");
            }

            // Check if discount code already exists
            var existingDiscount = await _unitOfWork.DiscountConfigurations.FirstOrDefaultAsync(
                d => d.DiscountCode == request.DiscountCode && d.CompanyId == companyId && !d.IsDeleted,
                cancellationToken);

            if (existingDiscount != null)
            {
                return ApiResponse<DiscountConfigurationDto>.ErrorResult("Discount code already exists");
            }

            var discountConfiguration = _mapper.Map<DiscountConfiguration>(request);
            discountConfiguration.CompanyId = companyId;
            discountConfiguration.CreatedBy = userId;

            var createdDiscount = await _unitOfWork.DiscountConfigurations.AddAsync(discountConfiguration, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var result = _mapper.Map<DiscountConfigurationDto>(createdDiscount);
            return ApiResponse<DiscountConfigurationDto>.SuccessResult(result, "Discount configuration created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<DiscountConfigurationDto>.ErrorResult($"Error creating discount configuration: {ex.Message}");
        }
    }
}
