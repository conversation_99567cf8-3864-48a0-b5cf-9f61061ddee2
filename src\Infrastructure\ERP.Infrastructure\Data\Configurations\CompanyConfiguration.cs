using ERP.Domain.Entities.Company;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ERP.Infrastructure.Data.Configurations;

public class CompanyConfiguration : IEntityTypeConfiguration<Company>
{
    public void Configure(EntityTypeBuilder<Company> builder)
    {
        builder.HasKey(c => c.Id);

        builder.Property(c => c.CompanyCode)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(c => c.CompanyNameEn)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(c => c.CompanyNameAr)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(c => c.TaxNumber)
            .HasMaxLength(50);

        builder.Property(c => c.CommercialRegistration)
            .HasMaxLength(50);

        builder.Property(c => c.Email)
            .HasMaxLength(100);

        builder.Property(c => c.Phone)
            .HasMaxLength(20);

        builder.Property(c => c.Address)
            .HasMaxLength(500);

        builder.Property(c => c.City)
            .HasMaxLength(100);

        builder.Property(c => c.Country)
            .HasMaxLength(100);

        builder.Property(c => c.PostalCode)
            .HasMaxLength(20);

        builder.Property(c => c.LogoPath)
            .HasMaxLength(500);

        builder.Property(c => c.BaseCurrency)
            .IsRequired()
            .HasMaxLength(3)
            .HasDefaultValue("SAR");

        // Indexes
        builder.HasIndex(c => c.CompanyCode).IsUnique();
        builder.HasIndex(c => c.TaxNumber);
        builder.HasIndex(c => c.CommercialRegistration);

        // Relationships
        builder.HasMany(c => c.Branches)
            .WithOne(b => b.Company)
            .HasForeignKey(b => b.CompanyId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(c => c.TaxConfigurations)
            .WithOne(t => t.Company)
            .HasForeignKey(t => t.CompanyId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(c => c.DiscountConfigurations)
            .WithOne(d => d.Company)
            .HasForeignKey(d => d.CompanyId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
