namespace ERP.DTOs.Inventory;

public class ItemCategoryDto
{
    public Guid Id { get; set; }
    public string CategoryCode { get; set; } = string.Empty;
    public string CategoryNameEn { get; set; } = string.Empty;
    public string CategoryNameAr { get; set; } = string.Empty;
    public string DescriptionEn { get; set; } = string.Empty;
    public string DescriptionAr { get; set; } = string.Empty;
    public Guid? ParentCategoryId { get; set; }
    public bool IsActive { get; set; }
}

public class ItemGroupDto
{
    public Guid Id { get; set; }
    public string GroupCode { get; set; } = string.Empty;
    public string GroupNameEn { get; set; } = string.Empty;
    public string GroupNameAr { get; set; } = string.Empty;
    public string DescriptionEn { get; set; } = string.Empty;
    public string DescriptionAr { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class UnitDto
{
    public Guid Id { get; set; }
    public string UnitCode { get; set; } = string.Empty;
    public string UnitNameEn { get; set; } = string.Empty;
    public string UnitNameAr { get; set; } = string.Empty;
    public string Symbol { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class BrandDto
{
    public Guid Id { get; set; }
    public string BrandNameEn { get; set; } = string.Empty;
    public string BrandNameAr { get; set; } = string.Empty;
    public string LogoPath { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class ManufacturerDto
{
    public Guid Id { get; set; }
    public string ManufacturerNameEn { get; set; } = string.Empty;
    public string ManufacturerNameAr { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

public class ItemTaxDto
{
    public Guid Id { get; set; }
    public string TaxCode { get; set; } = string.Empty;
    public string TaxNameEn { get; set; } = string.Empty;
    public string TaxNameAr { get; set; } = string.Empty;
    public decimal TaxRate { get; set; }
    public bool IsApplicable { get; set; }
}

public class ItemDiscountDto
{
    public Guid Id { get; set; }
    public string DiscountCode { get; set; } = string.Empty;
    public string DiscountNameEn { get; set; } = string.Empty;
    public string DiscountNameAr { get; set; } = string.Empty;
    public decimal DiscountValue { get; set; }
    public bool IsApplicable { get; set; }
}

public class ItemImageDto
{
    public Guid Id { get; set; }
    public string ImagePath { get; set; } = string.Empty;
    public string ImageName { get; set; } = string.Empty;
    public bool IsPrimary { get; set; }
    public int SortOrder { get; set; }
}

public class ItemUnitDto
{
    public Guid Id { get; set; }
    public UnitDto Unit { get; set; } = null!;
    public decimal ConversionFactor { get; set; }
    public decimal Price { get; set; }
    public bool IsDefault { get; set; }
}
