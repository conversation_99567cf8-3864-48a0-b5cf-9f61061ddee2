using ERP.Domain.Common;

namespace ERP.Domain.Entities.Company;

public class Branch : BaseEntity
{
    public string BranchCode { get; set; } = string.Empty;
    public string BranchNameEn { get; set; } = string.Empty;
    public string BranchNameAr { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public bool IsMainBranch { get; set; } = false;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public Company Company { get; set; } = null!;
    public ICollection<Sales.SalesInvoice> SalesInvoices { get; set; } = new List<Sales.SalesInvoice>();
    public ICollection<Inventory.ItemStock> ItemStocks { get; set; } = new List<Inventory.ItemStock>();
}
