using ERP.Domain.Entities.Inventory;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ERP.Infrastructure.Data.Configurations;

public class ItemConfiguration : IEntityTypeConfiguration<Item>
{
    public void Configure(EntityTypeBuilder<Item> builder)
    {
        builder.HasKey(i => i.Id);

        builder.Property(i => i.ItemCode)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(i => i.ItemNameEn)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(i => i.ItemNameAr)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(i => i.DescriptionEn)
            .HasMaxLength(1000);

        builder.Property(i => i.DescriptionAr)
            .HasMaxLength(1000);

        builder.Property(i => i.Barcode)
            .HasMaxLength(50);

        builder.Property(i => i.QRCode)
            .HasMaxLength(200);

        builder.Property(i => i.SKU)
            .HasMaxLength(50);

        builder.Property(i => i.CostPrice)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.SellingPrice)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.WholesalePrice)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.MinSellingPrice)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.Weight)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.Dimensions)
            .HasMaxLength(100);

        builder.Property(i => i.Color)
            .HasMaxLength(50);

        builder.Property(i => i.Size)
            .HasMaxLength(50);

        builder.Property(i => i.Model)
            .HasMaxLength(100);

        builder.Property(i => i.BatchNumber)
            .HasMaxLength(50);

        builder.Property(i => i.SerialNumber)
            .HasMaxLength(50);

        builder.Property(i => i.MinStockLevel)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.MaxStockLevel)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.ReorderLevel)
            .HasColumnType("decimal(18,4)");

        // Indexes
        builder.HasIndex(i => new { i.ItemCode, i.CompanyId }).IsUnique();
        builder.HasIndex(i => i.Barcode);
        builder.HasIndex(i => i.SKU);
        builder.HasIndex(i => i.ItemNameEn);
        builder.HasIndex(i => i.ItemNameAr);

        // Relationships
        builder.HasOne(i => i.ItemCategory)
            .WithMany(c => c.Items)
            .HasForeignKey(i => i.ItemCategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(i => i.ItemGroup)
            .WithMany(g => g.Items)
            .HasForeignKey(i => i.ItemGroupId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(i => i.BaseUnit)
            .WithMany(u => u.BaseUnitItems)
            .HasForeignKey(i => i.BaseUnitId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(i => i.Brand)
            .WithMany(b => b.Items)
            .HasForeignKey(i => i.BrandId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(i => i.Manufacturer)
            .WithMany(m => m.Items)
            .HasForeignKey(i => i.ManufacturerId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(i => i.Company)
            .WithMany(c => c.Items)
            .HasForeignKey(i => i.CompanyId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(i => i.ItemTaxes)
            .WithOne(it => it.Item)
            .HasForeignKey(it => it.ItemId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(i => i.ItemDiscounts)
            .WithOne(id => id.Item)
            .HasForeignKey(id => id.ItemId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(i => i.ItemUnits)
            .WithOne(iu => iu.Item)
            .HasForeignKey(iu => iu.ItemId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(i => i.ItemImages)
            .WithOne(ii => ii.Item)
            .HasForeignKey(ii => ii.ItemId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(i => i.ItemStocks)
            .WithOne(ist => ist.Item)
            .HasForeignKey(ist => ist.ItemId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
