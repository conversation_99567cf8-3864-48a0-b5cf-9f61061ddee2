using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class Brand : BaseEntity
{
    public string BrandNameEn { get; set; } = string.Empty;
    public string BrandNameAr { get; set; } = string.Empty;
    public string LogoPath { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public Company.Company Company { get; set; } = null!;
    public ICollection<Item> Items { get; set; } = new List<Item>();
}
