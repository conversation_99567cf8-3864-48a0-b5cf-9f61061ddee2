using ERP.Domain.Common;
using ERP.Domain.Enums;

namespace ERP.Domain.Entities.Sales;

public class SalesInvoiceDiscount : BaseEntity
{
    public Guid SalesInvoiceId { get; set; }
    public Guid? DiscountConfigurationId { get; set; }
    public DiscountType DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
    public decimal DiscountAmount { get; set; }
    
    // Navigation Properties
    public SalesInvoice SalesInvoice { get; set; } = null!;
    public Configuration.DiscountConfiguration? DiscountConfiguration { get; set; }
}
