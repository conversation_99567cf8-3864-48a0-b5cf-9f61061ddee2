2025-06-04 03:52:49.420 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.454 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.455 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.455 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.455 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.455 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.455 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.455 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.455 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:49.543 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:49.544 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.076 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.077 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.077 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.077 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.077 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.077 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.077 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.078 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.078 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:52:50.099 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.099 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:52:50.100 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:53:05.200 +03:00 [WRN] Entity 'ApplicationRole' has a global query filter defined and is the required end of a relationship with the entity 'ApplicationUserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:53:05.476 +03:00 [WRN] Entity 'ApplicationRole' has a global query filter defined and is the required end of a relationship with the entity 'ApplicationUserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.867 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.940 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.941 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.941 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.941 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.941 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.942 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.942 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:45.942 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:46.043 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.043 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:46.044 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.810 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.810 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.810 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.810 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.810 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.810 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.810 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.811 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.811 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:55:51.850 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.851 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.852 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.852 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.852 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.852 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.852 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:51.852 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:55:53.243 +03:00 [ERR] Failed executing DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Items] (
    [Id] uniqueidentifier NOT NULL,
    [ItemCode] nvarchar(50) NOT NULL,
    [ItemNameEn] nvarchar(200) NOT NULL,
    [ItemNameAr] nvarchar(200) NOT NULL,
    [DescriptionEn] nvarchar(1000) NOT NULL,
    [DescriptionAr] nvarchar(1000) NOT NULL,
    [Barcode] nvarchar(50) NOT NULL,
    [QRCode] nvarchar(200) NOT NULL,
    [SKU] nvarchar(50) NOT NULL,
    [ItemCategoryId] uniqueidentifier NOT NULL,
    [ItemGroupId] uniqueidentifier NOT NULL,
    [BaseUnitId] uniqueidentifier NOT NULL,
    [BrandId] uniqueidentifier NULL,
    [ManufacturerId] uniqueidentifier NULL,
    [CostPrice] decimal(18,4) NOT NULL,
    [SellingPrice] decimal(18,4) NOT NULL,
    [WholesalePrice] decimal(18,4) NOT NULL,
    [MinSellingPrice] decimal(18,4) NOT NULL,
    [Weight] decimal(18,4) NOT NULL,
    [Dimensions] nvarchar(100) NOT NULL,
    [Color] nvarchar(50) NOT NULL,
    [Size] nvarchar(50) NOT NULL,
    [Model] nvarchar(100) NOT NULL,
    [ExpiryDate] datetime2 NULL,
    [BatchNumber] nvarchar(50) NOT NULL,
    [SerialNumber] nvarchar(50) NOT NULL,
    [MinStockLevel] decimal(18,4) NOT NULL,
    [MaxStockLevel] decimal(18,4) NOT NULL,
    [ReorderLevel] decimal(18,4) NOT NULL,
    [IsActive] bit NOT NULL,
    [IsInventoryItem] bit NOT NULL,
    [IsSerialized] bit NOT NULL,
    [HasExpiryDate] bit NOT NULL,
    [ItemType] int NOT NULL,
    [CompanyId] uniqueidentifier NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] uniqueidentifier NOT NULL,
    [UpdatedBy] uniqueidentifier NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Items] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Items_Brands_BrandId] FOREIGN KEY ([BrandId]) REFERENCES [Brands] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Items_Companies_CompanyId] FOREIGN KEY ([CompanyId]) REFERENCES [Companies] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_ItemCategories_ItemCategoryId] FOREIGN KEY ([ItemCategoryId]) REFERENCES [ItemCategories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_ItemGroups_ItemGroupId] FOREIGN KEY ([ItemGroupId]) REFERENCES [ItemGroups] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_Manufacturers_ManufacturerId] FOREIGN KEY ([ManufacturerId]) REFERENCES [Manufacturers] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Items_Units_BaseUnitId] FOREIGN KEY ([BaseUnitId]) REFERENCES [Units] ([Id]) ON DELETE NO ACTION
);
2025-06-04 03:56:29.318 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.352 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.352 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.352 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.352 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.352 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.352 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.352 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.352 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:29.469 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.469 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.469 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.469 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.469 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:29.470 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.239 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.240 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.240 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.240 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.240 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.240 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.240 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.240 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.240 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.267 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.268 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.268 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.268 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.268 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.268 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.268 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:30.268 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.769 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.807 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.807 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.807 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.807 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.807 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.807 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.807 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.807 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.933 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.934 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.934 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.934 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.934 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:48.934 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.551 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:56:49.570 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.570 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:56:49.571 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.334 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.402 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.403 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.403 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.403 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.403 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.403 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.403 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.403 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:09.568 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.568 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.568 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.568 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.568 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.569 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.570 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.570 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.570 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.570 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:09.570 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:26.690 +03:00 [WRN] Entity 'ApplicationRole' has a global query filter defined and is the required end of a relationship with the entity 'ApplicationUserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:27.517 +03:00 [WRN] Entity 'ApplicationRole' has a global query filter defined and is the required end of a relationship with the entity 'ApplicationUserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:56.458 +03:00 [INF] Starting ERP API
2025-06-04 03:57:57.388 +03:00 [WRN] Entity 'ApplicationRole' has a global query filter defined and is the required end of a relationship with the entity 'ApplicationUserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:57.973 +03:00 [WRN] Entity 'ApplicationRole' has a global query filter defined and is the required end of a relationship with the entity 'ApplicationUserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.537 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.538 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.538 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.538 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.538 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.538 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.538 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.538 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.538 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:57:59.580 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.580 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.580 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.581 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.582 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.582 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.582 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.582 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.582 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:57:59.582 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'DiscountConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'ItemDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'Branch' has a global query filter defined and is the required end of a relationship with the entity 'ItemStock'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'ItemUnit'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceDiscount'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'Item' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLine'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'TaxConfiguration' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceLineTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.119 +03:00 [WRN] Entity 'SalesInvoice' has a global query filter defined and is the required end of a relationship with the entity 'SalesInvoiceTax'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'MaximumDiscount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'MinimumAmount' on entity type 'DiscountConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'TaxRate' on entity type 'TaxConfiguration'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'QuantityAvailable' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'QuantityOnHand' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'QuantityReserved' on entity type 'ItemStock'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'ConversionFactor' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.145 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'ItemUnit'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.146 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.146 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.146 +03:00 [WRN] No store type was specified for the decimal property 'DiscountAmount' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.146 +03:00 [WRN] No store type was specified for the decimal property 'DiscountValue' on entity type 'SalesInvoiceLineDiscount'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.146 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.146 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceLineTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.146 +03:00 [WRN] No store type was specified for the decimal property 'TaxAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:00.146 +03:00 [WRN] No store type was specified for the decimal property 'TaxableAmount' on entity type 'SalesInvoiceTax'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-06-04 03:58:03.610 +03:00 [INF] Database seeding completed successfully
2025-06-04 09:49:59.054 +03:00 [INF] Starting ERP API
