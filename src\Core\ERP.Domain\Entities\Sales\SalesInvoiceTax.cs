using ERP.Domain.Common;

namespace ERP.Domain.Entities.Sales;

public class SalesInvoiceTax : BaseEntity
{
    public Guid SalesInvoiceId { get; set; }
    public Guid TaxConfigurationId { get; set; }
    public decimal TaxableAmount { get; set; }
    public decimal TaxAmount { get; set; }
    
    // Navigation Properties
    public SalesInvoice SalesInvoice { get; set; } = null!;
    public Configuration.TaxConfiguration TaxConfiguration { get; set; } = null!;
}
