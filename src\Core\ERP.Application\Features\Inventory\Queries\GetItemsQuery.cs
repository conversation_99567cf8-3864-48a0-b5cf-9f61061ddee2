using AutoMapper;
using ERP.Application.Common.Interfaces;
using ERP.DTOs.Inventory;
using ERP.Shared.Extensions;
using ERP.Shared.Models;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace ERP.Application.Features.Inventory.Queries;

public class GetItemsQuery : IRequest<ApiResponse<PagedResult<ItemDto>>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public bool? IsActive { get; set; }
    public Guid? CategoryId { get; set; }
    public Guid? GroupId { get; set; }
    public Guid? BrandId { get; set; }
}

public class GetItemsQueryHandler : IRequestHandler<GetItemsQuery, ApiResponse<PagedResult<ItemDto>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetItemsQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<ApiResponse<PagedResult<ItemDto>>> Handle(GetItemsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var companyId = _httpContextAccessor.HttpContext?.User.GetCompanyId() ?? Guid.Empty;

            if (companyId == Guid.Empty)
            {
                return ApiResponse<PagedResult<ItemDto>>.ErrorResult("Company not found");
            }

            var (items, totalCount) = await _unitOfWork.Items.GetPagedAsync(
                request.PageNumber,
                request.PageSize,
                predicate: i => i.CompanyId == companyId && 
                               !i.IsDeleted &&
                               (request.IsActive == null || i.IsActive == request.IsActive) &&
                               (request.CategoryId == null || i.ItemCategoryId == request.CategoryId) &&
                               (request.GroupId == null || i.ItemGroupId == request.GroupId) &&
                               (request.BrandId == null || i.BrandId == request.BrandId) &&
                               (string.IsNullOrEmpty(request.SearchTerm) || 
                                i.ItemCode.Contains(request.SearchTerm) ||
                                i.ItemNameEn.Contains(request.SearchTerm) ||
                                i.ItemNameAr.Contains(request.SearchTerm) ||
                                i.Barcode.Contains(request.SearchTerm)),
                orderBy: i => i.ItemCode,
                ascending: true,
                includes: new[]
                {
                    i => i.ItemCategory,
                    i => i.ItemGroup,
                    i => i.BaseUnit,
                    i => i.Brand!,
                    i => i.Manufacturer!,
                    i => i.ItemTaxes,
                    i => i.ItemDiscounts,
                    i => i.ItemUnits,
                    i => i.ItemImages
                });

            var itemDtos = _mapper.Map<List<ItemDto>>(items);
            var pagedResult = new PagedResult<ItemDto>(itemDtos, totalCount, request.PageNumber, request.PageSize);

            return ApiResponse<PagedResult<ItemDto>>.SuccessResult(pagedResult);
        }
        catch (Exception ex)
        {
            return ApiResponse<PagedResult<ItemDto>>.ErrorResult($"Error retrieving items: {ex.Message}");
        }
    }
}
