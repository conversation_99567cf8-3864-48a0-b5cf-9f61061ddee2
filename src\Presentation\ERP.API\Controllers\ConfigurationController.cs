using ERP.Application.Features.Configuration.Commands;
using ERP.Application.Features.Configuration.Queries;
using ERP.DTOs.Configuration;
using ERP.Shared.Models;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ERP.API.Controllers;

[Route("api/configuration")]
public class ConfigurationController : BaseController
{
    private readonly IMediator _mediator;

    public ConfigurationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    #region Tax Configuration

    /// <summary>
    /// Get all tax configurations
    /// </summary>
    /// <param name="isActive">Filter by active status</param>
    /// <returns>List of tax configurations</returns>
    [HttpGet("taxes")]
    public async Task<ActionResult<ApiResponse<List<TaxConfigurationDto>>>> GetTaxes([FromQuery] bool? isActive = null)
    {
        var query = new GetTaxConfigurationsQuery { IsActive = isActive };
        var result = await _mediator.Send(query);
        return HandleResult(result);
    }

    /// <summary>
    /// Create a new tax configuration
    /// </summary>
    /// <param name="command">Tax configuration data</param>
    /// <returns>Created tax configuration</returns>
    [HttpPost("taxes")]
    public async Task<ActionResult<ApiResponse<TaxConfigurationDto>>> CreateTax([FromBody] CreateTaxConfigurationCommand command)
    {
        var result = await _mediator.Send(command);
        return HandleResult(result);
    }

    #endregion

    #region Discount Configuration

    /// <summary>
    /// Create a new discount configuration
    /// </summary>
    /// <param name="command">Discount configuration data</param>
    /// <returns>Created discount configuration</returns>
    [HttpPost("discounts")]
    public async Task<ActionResult<ApiResponse<DiscountConfigurationDto>>> CreateDiscount([FromBody] CreateDiscountConfigurationCommand command)
    {
        var result = await _mediator.Send(command);
        return HandleResult(result);
    }

    #endregion
}
