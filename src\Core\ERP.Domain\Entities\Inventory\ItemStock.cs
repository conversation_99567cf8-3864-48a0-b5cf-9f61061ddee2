using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class ItemStock : BaseEntity
{
    public Guid ItemId { get; set; }
    public Guid BranchId { get; set; }
    public decimal QuantityOnHand { get; set; }
    public decimal QuantityReserved { get; set; }
    public decimal QuantityAvailable { get; set; }
    public decimal AverageCost { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    
    // Navigation Properties
    public Item Item { get; set; } = null!;
    public Company.Branch Branch { get; set; } = null!;
}
