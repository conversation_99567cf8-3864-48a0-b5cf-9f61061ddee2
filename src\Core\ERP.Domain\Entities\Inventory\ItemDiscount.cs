using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class ItemDiscount : BaseEntity
{
    public Guid ItemId { get; set; }
    public Guid DiscountConfigurationId { get; set; }
    public bool IsApplicable { get; set; } = true;
    
    // Navigation Properties
    public Item Item { get; set; } = null!;
    public Configuration.DiscountConfiguration DiscountConfiguration { get; set; } = null!;
}
