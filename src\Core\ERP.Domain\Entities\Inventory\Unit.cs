using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class Unit : BaseEntity
{
    public string UnitCode { get; set; } = string.Empty;
    public string UnitNameEn { get; set; } = string.Empty;
    public string UnitNameAr { get; set; } = string.Empty;
    public string Symbol { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public Company.Company Company { get; set; } = null!;
    public ICollection<Item> BaseUnitItems { get; set; } = new List<Item>();
    public ICollection<ItemUnit> ItemUnits { get; set; } = new List<ItemUnit>();
    public ICollection<Sales.SalesInvoiceLine> SalesInvoiceLines { get; set; } = new List<Sales.SalesInvoiceLine>();
}
