using AutoMapper;
using ERP.Application.Common.Interfaces;
using ERP.Domain.Entities.Inventory;
using ERP.DTOs.Inventory;
using ERP.Shared.Extensions;
using ERP.Shared.Models;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace ERP.Application.Features.Inventory.Commands;

public class CreateItemCommand : CreateItemRequest, IRequest<ApiResponse<ItemDto>>
{
}

public class CreateItemCommandHandler : IRequestHandler<CreateItemCommand, ApiResponse<ItemDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CreateItemCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<ApiResponse<ItemDto>> Handle(CreateItemCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var companyId = _httpContextAccessor.HttpContext?.User.GetCompanyId() ?? Guid.Empty;
            var userId = _httpContextAccessor.HttpContext?.User.GetUserId() ?? Guid.Empty;

            if (companyId == Guid.Empty)
            {
                return ApiResponse<ItemDto>.ErrorResult("Company not found");
            }

            // Check if item code already exists
            var existingItem = await _unitOfWork.Items.FirstOrDefaultAsync(
                i => i.ItemCode == request.ItemCode && i.CompanyId == companyId && !i.IsDeleted,
                cancellationToken);

            if (existingItem != null)
            {
                return ApiResponse<ItemDto>.ErrorResult("Item code already exists");
            }

            // Validate foreign keys
            var category = await _unitOfWork.ItemCategories.GetByIdAsync(request.ItemCategoryId, cancellationToken);
            if (category == null || category.CompanyId != companyId)
            {
                return ApiResponse<ItemDto>.ErrorResult("Invalid item category");
            }

            var group = await _unitOfWork.ItemGroups.GetByIdAsync(request.ItemGroupId, cancellationToken);
            if (group == null || group.CompanyId != companyId)
            {
                return ApiResponse<ItemDto>.ErrorResult("Invalid item group");
            }

            var baseUnit = await _unitOfWork.Units.GetByIdAsync(request.BaseUnitId, cancellationToken);
            if (baseUnit == null || baseUnit.CompanyId != companyId)
            {
                return ApiResponse<ItemDto>.ErrorResult("Invalid base unit");
            }

            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            var item = _mapper.Map<Item>(request);
            item.CompanyId = companyId;
            item.CreatedBy = userId;

            var createdItem = await _unitOfWork.Items.AddAsync(item, cancellationToken);

            // Add item taxes
            foreach (var taxRequest in request.ItemTaxes)
            {
                var itemTax = new ItemTax
                {
                    ItemId = createdItem.Id,
                    TaxConfigurationId = taxRequest.TaxConfigurationId,
                    IsApplicable = taxRequest.IsApplicable,
                    CreatedBy = userId
                };
                await _unitOfWork.ItemTaxes.AddAsync(itemTax, cancellationToken);
            }

            // Add item discounts
            foreach (var discountRequest in request.ItemDiscounts)
            {
                var itemDiscount = new ItemDiscount
                {
                    ItemId = createdItem.Id,
                    DiscountConfigurationId = discountRequest.DiscountConfigurationId,
                    IsApplicable = discountRequest.IsApplicable,
                    CreatedBy = userId
                };
                await _unitOfWork.ItemDiscounts.AddAsync(itemDiscount, cancellationToken);
            }

            // Add item units
            foreach (var unitRequest in request.ItemUnits)
            {
                var itemUnit = new ItemUnit
                {
                    ItemId = createdItem.Id,
                    UnitId = unitRequest.UnitId,
                    ConversionFactor = unitRequest.ConversionFactor,
                    Price = unitRequest.Price,
                    IsDefault = unitRequest.IsDefault,
                    CreatedBy = userId
                };
                await _unitOfWork.ItemUnits.AddAsync(itemUnit, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            // Retrieve the created item with all related data
            var itemWithIncludes = await _unitOfWork.Items.GetByIdAsync(createdItem.Id,
                i => i.ItemCategory,
                i => i.ItemGroup,
                i => i.BaseUnit,
                i => i.Brand!,
                i => i.Manufacturer!,
                i => i.ItemTaxes,
                i => i.ItemDiscounts,
                i => i.ItemUnits,
                i => i.ItemImages);

            var result = _mapper.Map<ItemDto>(itemWithIncludes);
            return ApiResponse<ItemDto>.SuccessResult(result, "Item created successfully");
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            return ApiResponse<ItemDto>.ErrorResult($"Error creating item: {ex.Message}");
        }
    }
}
