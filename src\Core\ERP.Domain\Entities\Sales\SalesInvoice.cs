using ERP.Domain.Common;
using ERP.Domain.Enums;

namespace ERP.Domain.Entities.Sales;

public class SalesInvoice : BaseEntity
{
    public string InvoiceNumber { get; set; } = string.Empty;
    public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;
    public DateTime DueDate { get; set; }
    public Guid CustomerId { get; set; }
    public string Reference { get; set; } = string.Empty;
    public decimal SubTotal { get; set; }
    public decimal TotalTaxAmount { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public string CurrencyCode { get; set; } = "SAR";
    public decimal ExchangeRate { get; set; } = 1;
    public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
    public string Notes { get; set; } = string.Empty;
    public Guid CompanyId { get; set; }
    public Guid BranchId { get; set; }
    
    // Navigation Properties
    public Customers.Customer Customer { get; set; } = null!;
    public Company.Branch Branch { get; set; } = null!;
    public Company.Company Company { get; set; } = null!;
    public ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; } = new List<SalesInvoiceLine>();
    public ICollection<SalesInvoiceTax> SalesInvoiceTaxes { get; set; } = new List<SalesInvoiceTax>();
    public ICollection<SalesInvoiceDiscount> SalesInvoiceDiscounts { get; set; } = new List<SalesInvoiceDiscount>();
}
