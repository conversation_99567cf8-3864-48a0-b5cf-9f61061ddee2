using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class Manufacturer : BaseEntity
{
    public string ManufacturerNameEn { get; set; } = string.Empty;
    public string ManufacturerNameAr { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public Company.Company Company { get; set; } = null!;
    public ICollection<Item> Items { get; set; } = new List<Item>();
}
