using ERP.Domain.Common;

namespace ERP.Domain.Entities.Customers;

public class Customer : BaseEntity
{
    public string CustomerCode { get; set; } = string.Empty;
    public string CustomerNameEn { get; set; } = string.Empty;
    public string CustomerNameAr { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Mobile { get; set; } = string.Empty;
    public string TaxNumber { get; set; } = string.Empty;
    public string CommercialRegistration { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public decimal CreditLimit { get; set; }
    public int PaymentTerms { get; set; } = 30; // Days
    public bool IsActive { get; set; } = true;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public Company.Company Company { get; set; } = null!;
    public ICollection<Sales.SalesInvoice> SalesInvoices { get; set; } = new List<Sales.SalesInvoice>();
}
