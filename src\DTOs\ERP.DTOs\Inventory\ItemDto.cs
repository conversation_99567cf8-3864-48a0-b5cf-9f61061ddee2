using ERP.Domain.Enums;

namespace ERP.DTOs.Inventory;

public class ItemDto
{
    public Guid Id { get; set; }
    public string ItemCode { get; set; } = string.Empty;
    public string ItemNameEn { get; set; } = string.Empty;
    public string ItemNameAr { get; set; } = string.Empty;
    public string DescriptionEn { get; set; } = string.Empty;
    public string DescriptionAr { get; set; } = string.Empty;
    public string Barcode { get; set; } = string.Empty;
    public string QRCode { get; set; } = string.Empty;
    public string SKU { get; set; } = string.Empty;
    public decimal CostPrice { get; set; }
    public decimal SellingPrice { get; set; }
    public decimal WholesalePrice { get; set; }
    public decimal MinSellingPrice { get; set; }
    public decimal Weight { get; set; }
    public string Dimensions { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public string BatchNumber { get; set; } = string.Empty;
    public string SerialNumber { get; set; } = string.Empty;
    public decimal MinStockLevel { get; set; }
    public decimal MaxStockLevel { get; set; }
    public decimal ReorderLevel { get; set; }
    public bool IsActive { get; set; }
    public bool IsInventoryItem { get; set; }
    public bool IsSerialized { get; set; }
    public bool HasExpiryDate { get; set; }
    public ItemType ItemType { get; set; }
    
    // Related Data
    public ItemCategoryDto? ItemCategory { get; set; }
    public ItemGroupDto? ItemGroup { get; set; }
    public UnitDto? BaseUnit { get; set; }
    public BrandDto? Brand { get; set; }
    public ManufacturerDto? Manufacturer { get; set; }
    public List<ItemTaxDto> Taxes { get; set; } = new();
    public List<ItemDiscountDto> Discounts { get; set; } = new();
    public List<ItemImageDto> Images { get; set; } = new();
    public List<ItemUnitDto> Units { get; set; } = new();
}
