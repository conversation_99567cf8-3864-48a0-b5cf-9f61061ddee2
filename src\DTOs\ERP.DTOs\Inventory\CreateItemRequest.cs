using ERP.Domain.Enums;

namespace ERP.DTOs.Inventory;

public class CreateItemRequest
{
    public string ItemCode { get; set; } = string.Empty;
    public string ItemNameEn { get; set; } = string.Empty;
    public string ItemNameAr { get; set; } = string.Empty;
    public string DescriptionEn { get; set; } = string.Empty;
    public string DescriptionAr { get; set; } = string.Empty;
    public string Barcode { get; set; } = string.Empty;
    public string QRCode { get; set; } = string.Empty;
    public string SKU { get; set; } = string.Empty;
    public Guid ItemCategoryId { get; set; }
    public Guid ItemGroupId { get; set; }
    public Guid BaseUnitId { get; set; }
    public Guid? BrandId { get; set; }
    public Guid? ManufacturerId { get; set; }
    public decimal CostPrice { get; set; }
    public decimal SellingPrice { get; set; }
    public decimal WholesalePrice { get; set; }
    public decimal MinSellingPrice { get; set; }
    public decimal Weight { get; set; }
    public string Dimensions { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public DateTime? ExpiryDate { get; set; }
    public string BatchNumber { get; set; } = string.Empty;
    public string SerialNumber { get; set; } = string.Empty;
    public decimal MinStockLevel { get; set; }
    public decimal MaxStockLevel { get; set; }
    public decimal ReorderLevel { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsInventoryItem { get; set; } = true;
    public bool IsSerialized { get; set; } = false;
    public bool HasExpiryDate { get; set; } = false;
    public ItemType ItemType { get; set; }
    
    public List<CreateItemTaxRequest> ItemTaxes { get; set; } = new();
    public List<CreateItemDiscountRequest> ItemDiscounts { get; set; } = new();
    public List<CreateItemUnitRequest> ItemUnits { get; set; } = new();
}

public class CreateItemTaxRequest
{
    public Guid TaxConfigurationId { get; set; }
    public bool IsApplicable { get; set; } = true;
}

public class CreateItemDiscountRequest
{
    public Guid DiscountConfigurationId { get; set; }
    public bool IsApplicable { get; set; } = true;
}

public class CreateItemUnitRequest
{
    public Guid UnitId { get; set; }
    public decimal ConversionFactor { get; set; } = 1;
    public decimal Price { get; set; }
    public bool IsDefault { get; set; } = false;
}
