using ERP.Domain.Common;

namespace ERP.Domain.Entities.Inventory;

public class ItemGroup : BaseEntity
{
    public string GroupCode { get; set; } = string.Empty;
    public string GroupNameEn { get; set; } = string.Empty;
    public string GroupNameAr { get; set; } = string.Empty;
    public string DescriptionEn { get; set; } = string.Empty;
    public string DescriptionAr { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public Guid CompanyId { get; set; }
    
    // Navigation Properties
    public Company.Company Company { get; set; } = null!;
    public ICollection<Item> Items { get; set; } = new List<Item>();
}
