using ERP.Application.Features.Inventory.Commands;
using ERP.Application.Features.Inventory.Queries;
using ERP.DTOs.Inventory;
using ERP.Shared.Models;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace ERP.API.Controllers;

[Route("api/inventory")]
public class InventoryController : BaseController
{
    private readonly IMediator _mediator;

    public InventoryController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Get paginated list of items
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <returns>Paginated list of items</returns>
    [HttpGet("items")]
    public async Task<ActionResult<ApiResponse<PagedResult<ItemDto>>>> GetItems([FromQuery] GetItemsQuery query)
    {
        var result = await _mediator.Send(query);
        return HandleResult(result);
    }

    /// <summary>
    /// Create a new item
    /// </summary>
    /// <param name="command">Item data</param>
    /// <returns>Created item</returns>
    [HttpPost("items")]
    public async Task<ActionResult<ApiResponse<ItemDto>>> CreateItem([FromBody] CreateItemCommand command)
    {
        var result = await _mediator.Send(command);
        return HandleResult(result);
    }
}
